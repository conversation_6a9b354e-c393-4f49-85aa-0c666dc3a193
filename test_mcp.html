<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP 功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 8px;
            background-color: #2a2a2a;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .log {
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            margin: 5px;
            background-color: #333;
            color: white;
            border: 1px solid #555;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>MCP 功能测试页面</h1>
    <div style="background-color: #2d5a2d; padding: 10px; border-radius: 5px; margin-bottom: 20px;">
        <strong>✅ 修复状态:</strong> mcp_config.json 配置文件已创建，MCP代理应该可以正常工作了！
    </div>
    
    <div class="test-section">
        <h2>1. 测试 MCP 服务器列表</h2>
        <button onclick="testMcpList()">获取 MCP 服务器列表</button>
        <div id="listLog" class="log"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 测试基本连接</h2>
        <button onclick="testBasicConnection()">测试基本HTTP连接</button>
        <div id="basicLog" class="log"></div>
    </div>

    <div class="test-section">
        <h2>3. 测试 SSE 连接</h2>
        <select id="serverSelect">
            <option value="fetch">fetch</option>
            <option value="sequential">sequential</option>
            <option value="tavily">tavily</option>
        </select>
        <button onclick="testSseConnection()">测试 SSE 连接</button>
        <button onclick="stopSse()">停止连接</button>
        <div id="sseLog" class="log"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 测试完整 MCP 流程</h2>
        <input type="text" id="testUrl" placeholder="输入要测试的URL" value="https://www.example.com">
        <button onclick="testFullMcp()">测试完整流程</button>
        <div id="fullLog" class="log"></div>
    </div>

    <script>
        let currentEventSource = null;
        let currentSessionId = null;

        function log(elementId, message) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent += `[${timestamp}] ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).textContent = '';
        }

        async function testMcpList() {
            clearLog('listLog');
            log('listLog', '正在获取 MCP 服务器列表...');

            try {
                const response = await fetch('./mcp_proxy.php?action=list');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                log('listLog', `成功获取服务器列表: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log('listLog', `错误: ${error.message}`);
                log('listLog', `请确保访问正确的域名: ${window.location.origin}`);
            }
        }

        async function testBasicConnection() {
            clearLog('basicLog');
            log('basicLog', '测试基本HTTP连接...');

            try {
                // 测试代理文件是否存在
                const response = await fetch('./mcp_proxy.php?action=list');
                log('basicLog', `HTTP状态: ${response.status} ${response.statusText}`);
                log('basicLog', `响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);

                if (response.ok) {
                    const text = await response.text();
                    log('basicLog', `响应内容: ${text}`);

                    try {
                        const data = JSON.parse(text);
                        log('basicLog', '✓ JSON解析成功');
                        log('basicLog', `服务器数量: ${Object.keys(data).length}`);
                    } catch (jsonError) {
                        log('basicLog', `✗ JSON解析失败: ${jsonError.message}`);
                    }
                } else {
                    log('basicLog', '✗ HTTP请求失败');
                }
            } catch (error) {
                log('basicLog', `✗ 连接错误: ${error.message}`);
                log('basicLog', `当前URL: ${window.location.href}`);
            }
        }

        function testSseConnection() {
            const server = document.getElementById('serverSelect').value;
            clearLog('sseLog');
            log('sseLog', `正在连接到 ${server} 服务器...`);

            if (currentEventSource) {
                currentEventSource.close();
            }

            const sseUrl = `./mcp_proxy.php?action=sse&server=${encodeURIComponent(server)}`;
            log('sseLog', `SSE URL: ${sseUrl}`);

            try {
                currentEventSource = new EventSource(sseUrl);
                currentSessionId = null;

                currentEventSource.onopen = (event) => {
                    log('sseLog', '✓ SSE 连接已建立');
                    log('sseLog', `连接状态: ${currentEventSource.readyState}`);
                };

                currentEventSource.onmessage = (e) => {
                    log('sseLog', `收到消息: ${e.data}`);
                    log('sseLog', `消息类型: ${e.type}, 最后事件ID: ${e.lastEventId}`);

                    // 尝试提取 sessionId
                    if (!currentSessionId) {
                        try {
                            const url = new URL(e.data, window.location.origin);
                            const params = new URLSearchParams(url.search);
                            if (params.has('sessionId')) {
                                currentSessionId = params.get('sessionId');
                                log('sseLog', `✓ 提取到 sessionId: ${currentSessionId}`);
                            }
                        } catch (urlError) {
                            const match = e.data.match(/sessionId=([a-zA-Z0-9\-_]+)/i);
                            if (match) {
                                currentSessionId = match[1];
                                log('sseLog', `✓ 通过正则提取到 sessionId: ${currentSessionId}`);
                            }
                        }
                    }
                };

                currentEventSource.onerror = (error) => {
                    log('sseLog', `✗ SSE 连接错误`);
                    log('sseLog', `错误事件: ${JSON.stringify({type: error.type, target: error.target})}`);
                    log('sseLog', `就绪状态: ${currentEventSource.readyState}`);

                    const states = {
                        0: 'CONNECTING',
                        1: 'OPEN',
                        2: 'CLOSED'
                    };
                    log('sseLog', `状态含义: ${states[currentEventSource.readyState]}`);

                    if (currentEventSource.readyState === EventSource.CLOSED) {
                        log('sseLog', '连接已关闭，可能是服务器错误或网络问题');
                    } else if (currentEventSource.readyState === EventSource.CONNECTING) {
                        log('sseLog', '正在重连...');
                    }
                };

            } catch (error) {
                log('sseLog', `✗ 创建EventSource失败: ${error.message}`);
            }
        }

        function stopSse() {
            if (currentEventSource) {
                currentEventSource.close();
                currentEventSource = null;
                log('sseLog', 'SSE 连接已关闭');
            }
        }

        async function testFullMcp() {
            const server = document.getElementById('serverSelect').value;
            const testUrl = document.getElementById('testUrl').value;
            clearLog('fullLog');
            
            log('fullLog', `开始测试完整 MCP 流程 (${server})...`);
            log('fullLog', `测试 URL: ${testUrl}`);
            
            // 第一步：建立 SSE 连接
            if (currentEventSource) {
                currentEventSource.close();
            }
            
            currentEventSource = new EventSource(`./mcp_proxy.php?action=sse&server=${encodeURIComponent(server)}`);
            currentSessionId = null;
            
            currentEventSource.onopen = () => {
                log('fullLog', 'SSE 连接已建立');
            };
            
            currentEventSource.onmessage = async (e) => {
                log('fullLog', `收到 SSE 消息: ${e.data}`);
                
                // 提取 sessionId
                if (!currentSessionId) {
                    try {
                        const url = new URL(e.data, window.location.origin);
                        const params = new URLSearchParams(url.search);
                        if (params.has('sessionId')) {
                            currentSessionId = params.get('sessionId');
                            log('fullLog', `提取到 sessionId: ${currentSessionId}`);
                            
                            // 第二步：发送 POST 请求
                            try {
                                log('fullLog', '正在发送 POST 请求...');
                                const response = await fetch(`./mcp_proxy.php?action=post&server=${encodeURIComponent(server)}&sessionId=${encodeURIComponent(currentSessionId)}`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({ url: testUrl })
                                });
                                
                                const responseText = await response.text();
                                log('fullLog', `POST 响应状态: ${response.status}`);
                                log('fullLog', `POST 响应内容: ${responseText}`);
                                
                                currentEventSource.close();
                            } catch (postError) {
                                log('fullLog', `POST 请求错误: ${postError.message}`);
                            }
                        }
                    } catch (urlError) {
                        const match = e.data.match(/sessionId=([a-zA-Z0-9\-_]+)/i);
                        if (match) {
                            currentSessionId = match[1];
                            log('fullLog', `通过正则提取到 sessionId: ${currentSessionId}`);
                        }
                    }
                }
            };
            
            currentEventSource.onerror = (error) => {
                log('fullLog', `SSE 连接错误: ${error}`);
            };
        }
    </script>
</body>
</html>
