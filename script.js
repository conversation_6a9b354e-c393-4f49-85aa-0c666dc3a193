document.addEventListener('DOMContentLoaded', () => {
    // --- 应用程序初始化 ---
    const app = new ChatHubApp();
    app.init();
});

class ChatHubApp {
    // --- 构造函数和状态管理 ---
    constructor() {
        // 全局状态
        this.state = {
            models: [],
            messages: {},
            history: {},
            isLoading: {},
            currentLayout: 'columns',
            isSearchEnabled: false,
            isAuthenticated: false,
            mcpEnabled: false,
            mcpServers: [],
            mcpServersSelected: [],
            quickPhrases: [],
            uploadedFile: null,
            temperature: 0.7,
            contextLength: 10,
        };

        // DOM 元素缓存
        this.dom = {
            passwordScreen: document.getElementById('password-screen'),
            appScreen: document.getElementById('app'),
            passwordForm: document.getElementById('password-form'),
            passwordInput: document.getElementById('password-input'),
            passwordError: document.getElementById('password-error'),
            modelListContainer: document.getElementById('model-list'),
            chatGrid: document.getElementById('chat-grid'),
            chatInput: document.getElementById('chat-input'),
            sendButton: document.getElementById('send-button'),
            openSidebarBtn: document.getElementById('open-sidebar-btn'),
            closeSidebarBtn: document.getElementById('close-sidebar-btn'),
            sidebar: document.getElementById('sidebar'),
            layoutControls: document.getElementById('layout-controls'),
            globalNewChatBtn: document.getElementById('global-new-chat-btn'),
            searchToggleBtn: document.getElementById('search-toggle-btn'),
            searchOptions: document.getElementById('search-options'),
            searchResultsCount: document.getElementById('search-results-count'),
            settingsBtn: document.getElementById('settings-btn'),
            modalContainer: document.getElementById('modal-container'),
            mcpToggleBtn: document.getElementById('mcp-toggle-btn'),
            mcpOptions: document.getElementById('mcp-options'),
            mcpSelectionArea: document.getElementById('mcp-selection-area'),
            quickPhrasesBtn: document.getElementById('quick-phrases-btn'),
            uploadFileBtn: document.getElementById('upload-file-btn'),
            fileInput: document.getElementById('file-input'),
            fileAttachmentIndicator: document.getElementById('file-attachment-indicator'),
            dataManagementBtn: document.getElementById('data-management-btn'),
            announcementBtn: document.getElementById('announcement-btn'),
            parameterSettingsBtn: document.getElementById('parameter-settings-btn'),
        };

        // AbortController 用于中断正在进行的 fetch 请求
        this.controllers = {};
        // 用于渲染节流的计时器
        this.throttleTimers = {};
        // 浮窗
        this.quickPhrasesPopover = null;
        this.parameterPopover = null;
        this.sourcesPopover = null;
    }

    // --- 初始化方法 ---
    init() {
        this.injectStyles();
        lucide.createIcons();
        this.loadSettingsFromLocalStorage();
        this.loadQuickPhrases(); 
        this.loadParameters();
        this.checkSession();
        this.attachEventListeners();
        this.setupLayoutControls();
        this.fetchMcpServers();
        this.state.mcpEnabled = localStorage.getItem('chathub-mcp-enabled') === '1';
        try {
            this.state.mcpServersSelected = JSON.parse(localStorage.getItem('chathub-mcp-servers')) || [];
        } catch { this.state.mcpServersSelected = []; }
        this.renderMcpSelection();
        this.toggleMcpOptions(this.state.mcpEnabled);
    }
    
    // --- 样式注入 ---
    injectStyles() {
        const styleId = 'chathub-dynamic-styles';
        if (document.getElementById(styleId)) return;

        const style = this.createElement('style', {
            id: styleId,
            innerHTML: `
                .typing-cursor::after {
                    content: '▍';
                    animation: blink 1s step-end infinite;
                    margin-left: 3px;
                    font-size: 1.1em;
                    color: #63b3ed;
                }
                @keyframes blink { 50% { opacity: 0; } }
                @keyframes fadeOut { to { opacity: 0; transform: translateY(20px); } }
            `
        });
        document.head.appendChild(style);
    }
    
    // --- 本地存储 (LocalStorage) ---
    loadSettingsFromLocalStorage() {
        const savedModels = localStorage.getItem('chathub-models');
        if (savedModels) {
            try {
                this.state.models = JSON.parse(savedModels);
            } catch (e) {
                console.error("加载模型配置失败:", e);
                this.state.models = [];
            }
        }
    }

    saveModelsToLocalStorage() {
        localStorage.setItem('chathub-models', JSON.stringify(this.state.models));
    }

    loadQuickPhrases() {
        const savedPhrases = localStorage.getItem('chathub-quick-phrases');
        if (savedPhrases) {
            try {
                this.state.quickPhrases = JSON.parse(savedPhrases);
            } catch (e) {
                console.error("加载快捷短语失败:", e);
                this.state.quickPhrases = [];
            }
        }
    }

    saveQuickPhrases() {
        localStorage.setItem('chathub-quick-phrases', JSON.stringify(this.state.quickPhrases));
    }

    loadParameters() {
        const temp = localStorage.getItem('chathub-temperature');
        const context = localStorage.getItem('chathub-contextLength');
        if (temp) this.state.temperature = parseFloat(temp);
        if (context) this.state.contextLength = parseInt(context, 10);
    }
    
    // --- 后端通信 ---
    async apiCall(action, options = {}) {
        try {
            let url = './proxy.php';
            if (options.method === 'POST' && options.body && options.body instanceof FormData) {
                options.body.append('action', action);
            } else {
                url += `?action=${action}`;
            }
            const response = await fetch(url, options);
            if (!response.ok) {
                const errorResult = await response.json().catch(() => ({ message: response.statusText }));
                throw new Error(errorResult.message || `HTTP 错误: ${response.status}`);
            }
            return response.json();
        } catch (error) {
            console.error(`API 调用失败 (${action}):`, error);
            this.showToast(`错误: ${error.message}`, 'error');
            throw error;
        }
    }
    
    // --- 会话管理 ---
    async checkSession() {
        const token = sessionStorage.getItem('chathub-token');
        if (!token) return;

        try {
            const formData = new FormData();
            formData.append('token', token);
            const result = await this.apiCall('verify', { method: 'POST', body: formData });
            if (result.success) {
                this.startApplication();
            } else {
                sessionStorage.removeItem('chathub-token');
            }
        } catch (error) {
            // 错误已在 apiCall 中处理
        }
    }

    async login(password) {
        try {
            const formData = new FormData();
            formData.append('password', password);
            const result = await this.apiCall('login', { method: 'POST', body: formData });
            if (result.success) {
                sessionStorage.setItem('chathub-token', result.token);
                this.startApplication();
            } else {
                this.dom.passwordError.textContent = result.message || '未知错误';
                this.dom.passwordError.classList.remove('hidden');
            }
        } catch (error) {
            this.dom.passwordError.textContent = error.message;
            this.dom.passwordError.classList.remove('hidden');
        }
    }

    async startApplication() {
        this.state.isAuthenticated = true;
        this.dom.passwordScreen.classList.add('hidden');
        this.dom.appScreen.classList.remove('hidden');
        this.dom.appScreen.classList.add('fade-in');
        
        if (!this.state.models || this.state.models.length === 0) {
            try {
                const result = await this.apiCall('get_defaults');
                if (result.success) {
                    this.state.models = result.models.map(m => ({ ...m, isPreset: true, supports_vision: m.supports_vision || false }));
                    this.saveModelsToLocalStorage();
                }
            } catch (error) {
                this.showToast('无法加载默认模型', 'error');
            }
        }

        this.loadConversations();
        this.renderAll();
    }
    
    // --- 渲染和 UI 更新 ---
    renderAll() {
        this.renderModelList();
        this.renderChatGrid();
    }

    renderModelList() {
        const { modelListContainer } = this.dom;
        modelListContainer.innerHTML = '<h2 class="text-xs font-semibold text-gray-500 uppercase px-2">可用模型</h2>';
        
        this.state.models.forEach(model => {
            const iconHtml = this.getIconHtml(model.icon, 'w-5 h-5');
            const modelItem = this.createElement('div', {
                className: 'flex items-center justify-between p-2 rounded-lg hover:bg-gray-700/50',
                innerHTML: `
                    <div class="flex items-center gap-3">
                        ${iconHtml}
                        <span class="text-sm">${this.escapeHtml(model.name)}</span>
                        ${model.supports_vision ? '<i data-lucide="camera" class="w-4 h-4 text-blue-400" title="支持视觉"></i>' : ''}
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" ${model.enabled ? 'checked' : ''} data-id="${model.id}" class="model-toggle sr-only peer">
                        <div class="w-9 h-5 bg-gray-600 rounded-full peer peer-focus:ring-2 peer-focus:ring-blue-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>`
            });
            modelListContainer.appendChild(modelItem);
        });
        lucide.createIcons();
    }

    renderChatGrid() {
        const { chatGrid } = this.dom;
        const enabledModels = this.state.models.filter(m => m.enabled);
        
        if (enabledModels.length === 0) {
            chatGrid.className = 'flex-1 p-4 flex items-center justify-center';
            chatGrid.innerHTML = `
                <div class="text-center text-gray-500 slide-in-up">
                    <i data-lucide="layout-grid" class="w-16 h-16 mx-auto mb-4"></i>
                    <h2 class="text-2xl font-semibold text-gray-300">欢迎来到 Chat Hub Pro</h2>
                    <p class="mt-2">选择或添加并启用您的第一个模型以开始。</p>
                </div>`;
            lucide.createIcons();
            return;
        }

        let gridLayoutClasses = 'grid ';
        const colCount = Math.min(enabledModels.length, 3);
        gridLayoutClasses += this.state.currentLayout === 'rows' 
            ? 'grid-cols-1' 
            : `grid-cols-1 md:grid-cols-${colCount}`;

        chatGrid.className = `flex-1 p-4 overflow-y-auto gap-4 ${gridLayoutClasses} custom-scrollbar`;
        chatGrid.innerHTML = '';
        
        enabledModels.forEach(model => {
            const iconHtml = this.getIconHtml(model.icon, 'w-5 h-5');
            const safeModelId = this.sanitizeForId(model.id);
            const chatWindow = this.createElement('div', {
                id: `chat-window-${safeModelId}`,
                className: 'bg-gray-800/50 rounded-xl border border-gray-700/50 flex flex-col h-full min-h-[300px] slide-in-up',
                innerHTML: `
                    <header class="flex items-center justify-between p-3 border-b border-gray-700/50 flex-shrink-0">
                        <div class="flex items-center gap-2">${iconHtml}<h3 class="font-semibold text-sm">${this.escapeHtml(model.name)}</h3></div>
                        <div class="flex items-center gap-1">
                            <button class="history-btn p-1.5 rounded-md hover:bg-gray-700" title="聊天记录" data-model-id="${model.id}">
                                <i data-lucide="history" class="w-4 h-4 text-gray-400 pointer-events-none"></i>
                            </button>
                            <button class="new-chat-btn p-1.5 rounded-md hover:bg-gray-700" title="新对话 (存档当前)" data-model-id="${model.id}">
                                <i data-lucide="file-plus-2" class="w-4 h-4 text-gray-400 pointer-events-none"></i>
                            </button>
                        </div>
                    </header>
                    <main class="flex-1 p-3 overflow-y-auto custom-scrollbar" id="messages-${safeModelId}"></main>
                `
            });
            chatGrid.appendChild(chatWindow);
            this.renderMessages(model.id);
        });
        lucide.createIcons();
    }

    renderMessages(modelId) {
        const safeModelId = this.sanitizeForId(modelId);
        const container = document.getElementById(`messages-${safeModelId}`);
        if (!container) return;

        const model = this.state.models.find(m => m.id === modelId);
        const iconHtml = this.getIconHtml(model?.icon, 'w-6 h-6');

        const shouldScrollToBottom = container.scrollHeight - container.clientHeight <= container.scrollTop + 30;

        container.innerHTML = '';

        (this.state.messages[modelId] || []).forEach((msg, index) => {
            const isLastMessage = index === this.state.messages[modelId].length - 1;
            const isAiTyping = isLastMessage && this.state.isLoading[modelId];

            const msgEl = this.createElement('div');

            if (msg.role === 'user') {
                msgEl.className = 'flex items-end gap-2 my-4 justify-end group relative';
                
                let combinedContentForCopy = msg.content || '';
                if (msg.attachment) {
                    combinedContentForCopy += `\n[附件: ${msg.attachment.name}]`;
                }

                let messageBlocks = '';
                if (msg.content) {
                    messageBlocks += `<div class="px-4 py-3 rounded-xl whitespace-pre-wrap break-words bg-blue-600 text-white rounded-br-none">${this.escapeHtml(msg.content)}</div>`;
                }
                if (msg.attachment) {
                    const isImage = msg.attachment.type.startsWith('image/');
                    messageBlocks += `
                        <div class="mt-2 p-2 bg-blue-700/60 rounded-lg w-full max-w-full">
                            <div class="flex items-center gap-2 text-sm">
                                <i data-lucide="${isImage ? 'file-image' : 'file-text'}" class="w-5 h-5 text-gray-300 flex-shrink-0"></i>
                                <span class="text-gray-200 truncate">${this.escapeHtml(msg.attachment.name)}</span>
                            </div>
                            ${isImage ? `<img src="${msg.attachment.content}" class="mt-2 rounded-md max-w-full h-auto max-h-48 cursor-pointer" onclick="window.open(this.src)">` : ''}
                        </div>`;
                }
                
                msgEl.innerHTML = `
                    <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                         <button class="resend-btn p-1.5 rounded-md hover:bg-gray-700" title="重发" data-model-id="${modelId}" data-message-index="${index}">
                            <i data-lucide="rotate-cw" class="w-4 h-4 text-gray-400 pointer-events-none"></i>
                        </button>
                        <button class="copy-btn p-1.5 rounded-md hover:bg-gray-700" title="复制" data-content="${this.escapeHtml(combinedContentForCopy)}">
                            <i data-lucide="copy" class="w-4 h-4 text-gray-400 pointer-events-none"></i>
                        </button>
                    </div>
                    <div class="flex flex-col items-end max-w-[85%] slide-in-up">
                        ${messageBlocks}
                    </div>
                    <div class="flex-shrink-0">
                         <i data-lucide="user" class="w-6 h-6 text-blue-400"></i>
                    </div>`;

            } else if (msg.role === 'assistant') {
                 if (msg.content === '' && isAiTyping) {
                    msgEl.className = 'flex items-start gap-3 my-4';
                    msgEl.innerHTML = `<div class="flex-shrink-0">${iconHtml}</div><div class="px-4 py-3 rounded-xl bg-gray-700 text-gray-200 rounded-bl-none flex items-center slide-in-up"><i data-lucide="loader" class="animate-spin w-5 h-5 mr-2"></i> 正在思考...</div>`;
                } else {
                    const safeMsgId = `msg-${safeModelId}-${index}`;
                    msgEl.className = 'flex items-start gap-3 my-4 group relative';
                    
                    let sourcesHtml = '';
                    if (msg.sources && msg.sources.length > 0) {
                        sourcesHtml = `
                            <div class="mt-2">
                                <button class="show-sources-btn flex items-center gap-1.5 text-xs text-gray-400 hover:text-gray-200" data-model-id="${modelId}" data-message-index="${index}">
                                    <i data-lucide="book-marked" class="w-3 h-3"></i>
                                    ${msg.sources.length}个引用内容
                                </button>
                            </div>
                        `;
                    }

                    const renderedMarkdown = marked.parse(msg.content);
                    const streamingIndicator = isAiTyping ? `<span class="inline-flex items-center text-xs text-gray-400 ml-2"><span class="typing-cursor"></span>AI还在输入中...</span>` : '';
                    
                    msgEl.innerHTML = `
                        <div class="flex-shrink-0">${iconHtml}</div>
                        <div class="flex flex-col w-full max-w-[85%]">
                            <div id="${safeMsgId}" class="px-4 py-3 rounded-xl break-words bg-gray-700 text-gray-200 rounded-bl-none prose prose-sm slide-in-up">${renderedMarkdown}${streamingIndicator}</div>
                            ${sourcesHtml}
                        </div>
                         <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 self-start pt-2">
                            <button class="copy-btn p-1.5 rounded-md hover:bg-gray-700" title="复制" data-content="${this.escapeHtml(msg.content)}">
                                <i data-lucide="copy" class="w-4 h-4 text-gray-400 pointer-events-none"></i>
                            </button>
                        </div>`;
                }
            } else if (msg.role === 'mcp_status') {
                // **MODIFIED**: Reworked MCP status message rendering for better styling and to show content.
                msgEl.className = 'flex items-start gap-3 my-4';
                
                let statusIcon;
                let statusText;
                let contentHtml = '';

                switch(msg.status) {
                    case 'loading':
                        statusIcon = '<i data-lucide="loader" class="w-4 h-4 animate-spin"></i>';
                        statusText = '正在调用MCP...';
                        break;
                    case 'complete':
                        statusIcon = '<i data-lucide="check-circle-2" class="w-4 h-4 text-green-400"></i>';
                        statusText = '调用完成';
                        if (msg.content) {
                            let formattedContent = this.escapeHtml(msg.content);
                            try {
                                const parsed = JSON.parse(msg.content);
                                formattedContent = `<pre class="text-xs whitespace-pre-wrap break-all">${this.escapeHtml(JSON.stringify(parsed, null, 2))}</pre>`;
                            } catch(e) { /* Not JSON, leave as plain text */ }
                            
                            contentHtml = `<div class="mt-2 p-3 bg-black/20 rounded-lg border border-gray-600/50 text-gray-300 text-sm">${formattedContent}</div>`;
                        }
                        break;
                    case 'error':
                        statusIcon = '<i data-lucide="alert-triangle" class="w-4 h-4 text-red-400"></i>';
                        statusText = '调用失败';
                        if (msg.content) {
                             contentHtml = `<div class="mt-2 p-3 bg-red-900/30 rounded-lg border border-red-500/50 text-red-300 text-sm">${this.escapeHtml(msg.content)}</div>`;
                        }
                        break;
                }

                msgEl.innerHTML = `
                    <div class="flex-shrink-0 pt-1">
                        <i data-lucide="server-cog" class="w-6 h-6 text-blue-400 opacity-80"></i>
                    </div>
                    <div class="flex-1 max-w-[85%]">
                        <div class="px-4 py-3 rounded-xl bg-gray-700/50 border border-dashed border-gray-600/70 text-gray-400 text-sm rounded-bl-none flex flex-col gap-2 slide-in-up">
                            <div class="flex items-center gap-2 font-medium">
                                ${statusIcon}
                                <span>[MCP:${this.escapeHtml(msg.serverKey)}] ${statusText}</span>
                            </div>
                            ${contentHtml}
                        </div>
                    </div>
                `;
            }
            container.appendChild(msgEl);
            if (msg.role === 'assistant' && msg.content) {
                this.addCodeButtons(msgEl);
            }
        });
        
        lucide.createIcons();
        if (shouldScrollToBottom) {
            container.scrollTop = container.scrollHeight;
        }
    }

    // --- 事件监听器 ---
    attachEventListeners() {
        this.dom.passwordForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.login(this.dom.passwordInput.value);
        });
        
        this.dom.sendButton.addEventListener('click', () => this.handleSendMessage());
        this.dom.chatInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.handleSendMessage();
            }
            this.adjustTextareaHeight();
        });

        this.dom.modelListContainer.addEventListener('change', (e) => {
            if (e.target.classList.contains('model-toggle')) {
                const modelId = e.target.dataset.id;
                const model = this.state.models.find(m => m.id === modelId);
                if (model) {
                    model.enabled = e.target.checked;
                    this.saveModelsToLocalStorage();
                    this.renderAll();
                }
            }
        });

        this.dom.chatGrid.addEventListener('click', (e) => {
            const button = e.target.closest('button');
            if (!button) return;

            if (button.classList.contains('copy-btn')) {
                this.handleCopy(button, button.dataset.content);
            } else if (button.classList.contains('resend-btn')) {
                const modelId = button.dataset.modelId;
                const messageIndex = parseInt(button.dataset.messageIndex, 10);
                this.handleResend(modelId, messageIndex);
            } else if (button.classList.contains('code-copy-btn')) {
                const pre = button.closest('pre');
                const code = pre.querySelector('code');
                if (code) this.handleCopy(button, code.textContent, 'copy');
            } else if (button.classList.contains('new-chat-btn')) {
                this.newConversation(button.dataset.modelId);
            } else if (button.classList.contains('history-btn')) {
                this.openModal(new HistoryModal(this, button.dataset.modelId));
            } else if (button.classList.contains('show-sources-btn')) {
                this.toggleSourcesPopover(button);
            } 
        });

        this.dom.globalNewChatBtn.addEventListener('click', () => {
            this.state.models.filter(m => m.enabled).forEach(m => this.newConversation(m.id));
        });

        this.dom.settingsBtn.addEventListener('click', () => {
            this.openModal(new SettingsModal(this));
        });

        this.dom.dataManagementBtn.addEventListener('click', () => {
            this.openModal(new DataManagementModal(this));
        });

        this.dom.announcementBtn.addEventListener('click', () => this.handleShowAnnouncement());

        this.dom.openSidebarBtn.addEventListener('click', () => this.dom.sidebar.classList.remove('-translate-x-full'));
        this.dom.closeSidebarBtn.addEventListener('click', () => this.dom.sidebar.classList.add('-translate-x-full'));
        this.dom.searchToggleBtn.addEventListener('click', () => this.toggleSearch());

        this.dom.quickPhrasesBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleQuickPhrasesPopover();
        });

        this.dom.parameterSettingsBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleParameterPopover();
        });

        this.dom.uploadFileBtn.addEventListener('click', () => this.dom.fileInput.click());
        this.dom.fileInput.addEventListener('change', (e) => this.handleFileUpload(e));

        this.dom.mcpToggleBtn.addEventListener('click', () => {
            this.state.mcpEnabled = !this.state.mcpEnabled;
            localStorage.setItem('chathub-mcp-enabled', this.state.mcpEnabled ? '1' : '0');
            this.toggleMcpOptions(this.state.mcpEnabled);
            this.showToast(this.state.mcpEnabled ? 'MCP已启用' : 'MCP已关闭', 'info');
        });

        this.dom.mcpSelectionArea.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox' && e.target.name === 'mcp-server') {
                const checked = Array.from(this.dom.mcpSelectionArea.querySelectorAll('input[name="mcp-server"]:checked')).map(i => i.value);
                this.state.mcpServersSelected = checked;
                localStorage.setItem('chathub-mcp-servers', JSON.stringify(this.state.mcpServersSelected));
                this.renderMcpSelection();
            }
        });
    }
    
    // --- 核心功能方法 ---
    handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (file.size > 5 * 1024 * 1024) { // 限制 5MB
            this.showToast('文件太大，请选择小于 5MB 的文件。', 'error');
            event.target.value = '';
            return;
        }
        
        const reader = new FileReader();
        const isImage = file.type.startsWith('image/');

        reader.onload = (e) => {
            this.state.uploadedFile = {
                name: file.name,
                type: file.type,
                content: e.target.result, // base64 for images, text for others
            };
            this.renderFileAttachmentIndicator();
            this.showToast(`文件 "${file.name}" 已附加`, 'success');
        };
        reader.onerror = () => {
            this.showToast('读取文件时发生错误。', 'error');
        };

        if (isImage) {
            reader.readAsDataURL(file); // Read image as base64
        } else {
            reader.readAsText(file); // Read other files as text
        }
        
        event.target.value = '';
    }

    renderFileAttachmentIndicator() {
        const { fileAttachmentIndicator } = this.dom;
        if (this.state.uploadedFile) {
            const isImage = this.state.uploadedFile.type.startsWith('image/');
            fileAttachmentIndicator.innerHTML = `
                <div class="flex items-center justify-between p-2 bg-gray-700/50 rounded-lg text-sm">
                    <div class="flex items-center gap-2 text-gray-300 truncate">
                        ${isImage ? `<img src="${this.state.uploadedFile.content}" class="w-8 h-8 rounded-md object-cover">` : `<i data-lucide="file-text" class="w-5 h-5 flex-shrink-0"></i>`}
                        <span class="truncate">${this.escapeHtml(this.state.uploadedFile.name)}</span>
                    </div>
                    <button id="remove-attachment-btn" class="p-1 hover:bg-red-500/20 rounded-full">
                        <i data-lucide="x" class="w-4 h-4 text-red-400"></i>
                    </button>
                </div>
            `;
            lucide.createIcons();
            fileAttachmentIndicator.querySelector('#remove-attachment-btn').addEventListener('click', () => this.removeFileAttachment());
        } else {
            fileAttachmentIndicator.innerHTML = '';
        }
    }

    removeFileAttachment() {
        this.state.uploadedFile = null;
        this.renderFileAttachmentIndicator();
    }

    handleResend(modelId, messageIndex) {
        const messageToResend = this.state.messages[modelId]?.[messageIndex];
        if (!messageToResend) return;
    
        this.dom.chatInput.value = messageToResend.content || '';
        this.adjustTextareaHeight();
    
        if (messageToResend.attachment) {
            this.state.uploadedFile = { ...messageToResend.attachment };
            this.renderFileAttachmentIndicator();
        } else {
            this.removeFileAttachment();
        }
    
        this.dom.chatInput.focus();
        this.showToast('消息已准备重发', 'info');
    }

    processHistoryForApi(history) {
        return history.map(msg => {
            if (msg.role === 'mcp_status') {
                return null;
            }
            if (msg.role !== 'user') {
                return msg;
            }

            const isImage = msg.attachment && msg.attachment.type.startsWith('image/');
            
            if (isImage) {
                const content = [];
                if (msg.content) {
                    content.push({ type: 'text', text: msg.content });
                }
                content.push({
                    type: 'image_url',
                    image_url: { url: msg.attachment.content }
                });
                return { role: 'user', content };
            } 
            else {
                let combinedContent = msg.content || '';
                if (msg.attachment) { // Text file
                    const fileBlock = `\n\n--- 文件: ${msg.attachment.name} ---\n${msg.attachment.content}\n--- 文件内容结束 ---`;
                    combinedContent = combinedContent ? `${combinedContent}${fileBlock}` : fileBlock.trim();
                }
                return { role: 'user', content: combinedContent };
            }
        }).filter(Boolean);
    }

    async handleSendMessage() {
        const text = this.dom.chatInput.value.trim();
        const file = this.state.uploadedFile;

        if (!text && !file) return;

        const enabledModels = this.state.models.filter(m => m.enabled);
        if (enabledModels.length === 0) {
            this.showToast('请先启用至少一个模型', 'warning');
            return;
        }

        if (file && file.type.startsWith('image/')) {
            const nonVisionModels = enabledModels.filter(m => !m.supports_vision);
            if (nonVisionModels.length > 0) {
                this.showToast(`模型 ${nonVisionModels.map(m=>m.name).join(', ')} 不支持图片，将跳过。`, 'warning');
            }
        }

        const userMessage = {
            role: 'user',
            content: text,
            attachment: file,
        };

        this.dom.chatInput.value = '';
        this.adjustTextareaHeight();
        this.removeFileAttachment();

        enabledModels.forEach(model => {
            if (file && file.type.startsWith('image/') && !model.supports_vision) {
                return;
            }

            if (!this.state.messages[model.id]) {
                this.state.messages[model.id] = [];
            }
            this.state.messages[model.id].push(userMessage);
            const userMessageIndex = this.state.messages[model.id].length - 1;

            if (this.state.mcpEnabled && this.state.mcpServersSelected.length > 0) {
                const contentForMcp = text;
                this.state.mcpServersSelected.forEach(serverKey => {
                    this.startMcpSse(serverKey, contentForMcp, model.id, userMessageIndex);
                });
            }

            this.state.messages[model.id].push({ role: 'assistant', content: '' });
            const assistantMsgIndex = this.state.messages[model.id].length - 1;

            this.state.isLoading[model.id] = true;
            this.renderMessages(model.id);

            this.streamAssistantResponse(model, assistantMsgIndex);
        });
        
        this.saveConversations();
    }
    
    async streamAssistantResponse(model, msgIndex) {
        if (!model.api_key) {
            this.showToast(`模型 [${model.name}] 未配置 API Key`, 'error');
            this.state.isLoading[model.id] = false;
            this.renderMessages(model.id);
            return;
        }
        
        const controller = new AbortController();
        this.controllers[model.id] = controller;
        
        let historyForApi = this.processHistoryForApi(this.state.messages[model.id].slice(0, msgIndex));
        
        if (this.state.contextLength > 0) {
            historyForApi = historyForApi.slice(-this.state.contextLength);
        } else if (historyForApi.length > 0) {
            historyForApi = [historyForApi[historyForApi.length - 1]];
        }


        if (historyForApi.length === 0) {
            this.state.isLoading[model.id] = false;
            const assistantMessage = this.state.messages[model.id][msgIndex];
            assistantMessage.content = "[消息被忽略，因为它只包含一个不支持的附件]";
            this.renderMessages(model.id);
            return;
        }

        try {
            const response = await fetch('./proxy.php?action=chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    modelConfig: model,
                    history: historyForApi,
                    use_search: this.state.isSearchEnabled,
                    max_results: this.dom.searchResultsCount.value,
                    temperature: this.state.temperature,
                }),
                signal: controller.signal,
            });

            if (!response.ok || !response.body) {
                const errorText = await response.text();
                throw new Error(`HTTP 错误: ${response.status}. ${errorText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                let boundary;
                while ((boundary = buffer.indexOf('\n\n')) >= 0) {
                    const messageBlock = buffer.slice(0, boundary);
                    buffer = buffer.slice(boundary + 2);
                    
                    let eventType = 'message';
                    const dataLines = [];

                    messageBlock.split('\n').forEach(line => {
                        if (line.startsWith('event:')) {
                            eventType = line.substring(6).trim();
                        } else if (line.startsWith('data:')) {
                            dataLines.push(line.substring(5).trim());
                        }
                    });

                    if (eventType === 'sources') {
                        const fullData = dataLines.join('');
                        try {
                            const sources = JSON.parse(fullData);
                            this.state.messages[model.id][msgIndex].sources = sources;
                        } catch (err) {
                            console.error("解析 sources 事件失败:", err, "数据:", fullData);
                        }
                    } else if (eventType === 'message') {
                        for (const data of dataLines) {
                            if (data === '[DONE]') continue;
                            if (!data) continue;
                            try {
                                const parsed = JSON.parse(data);
                                let content_chunk = '';
                                if (parsed.choices && parsed.choices[0]?.delta?.content) {
                                    content_chunk = parsed.choices[0].delta.content;
                                } else if (parsed.type === 'content_block_delta' && parsed.delta?.type === 'text_delta') {
                                    content_chunk = parsed.delta.text;
                                }
                                if (content_chunk) {
                                    this.state.messages[model.id][msgIndex].content += content_chunk;
                                }
                            } catch (e) {
                                console.error("解析 SSE 数据失败:", e, "数据:", data);
                            }
                        }
                    }
                }
                // Throttle UI updates
                if (!this.throttleTimers[model.id]) {
                    this.throttleTimers[model.id] = setTimeout(() => {
                        this.renderMessages(model.id);
                        delete this.throttleTimers[model.id];
                    }, 100);
                }
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error(`[${model.name}] 消息发送失败:`, error);
                this.state.messages[model.id][msgIndex].content = `抱歉，与服务器的连接发生错误。`;
            }
        } finally {
            if (this.throttleTimers[model.id]) {
                clearTimeout(this.throttleTimers[model.id]);
                delete this.throttleTimers[model.id];
            }
            this.state.isLoading[model.id] = false;
            this.renderMessages(model.id);
            this.saveConversations();
        }
    }

    newConversation(modelId) {
        if (this.controllers[modelId]) {
            this.controllers[modelId].abort();
        }

        const currentMessages = this.state.messages[modelId];
        if (currentMessages && currentMessages.length > 0) {
            let title = 'Archived Chat';
            const firstUserMessage = currentMessages.find(m => m.role === 'user');
            if (firstUserMessage) {
                title = firstUserMessage.content || `Attachment: ${firstUserMessage.attachment?.name}`;
            }

            const conversationToArchive = {
                id: Date.now() + Math.random(),
                title: title,
                messages: JSON.parse(JSON.stringify(currentMessages))
            };
            if (!this.state.history[modelId]) {
                this.state.history[modelId] = [];
            }
            this.state.history[modelId].unshift(conversationToArchive);
        }
        
        this.state.messages[modelId] = [];
        this.state.isLoading[modelId] = false;
        this.renderMessages(modelId);
        
        this.saveConversations();
        this.showToast('新对话已开始', 'success');
    }

    // --- 辅助方法 ---
    loadConversations() {
        try {
            const savedMessages = localStorage.getItem('chathub-messages');
            const savedHistory = localStorage.getItem('chathub-history');
            if (savedMessages) this.state.messages = JSON.parse(savedMessages);
            if (savedHistory) this.state.history = JSON.parse(savedHistory);
            
            this.state.models.forEach(m => {
                if (!this.state.messages[m.id]) this.state.messages[m.id] = [];
                if (!this.state.history[m.id]) this.state.history[m.id] = [];
                this.state.isLoading[m.id] = false;
            });
        } catch (e) {
            console.error("加载会话失败:", e);
        }
    }

    saveConversations() {
        try {
            localStorage.setItem('chathub-messages', JSON.stringify(this.state.messages));
            localStorage.setItem('chathub-history', JSON.stringify(this.state.history));
        } catch (e) {
            console.error("保存会话失败:", e);
        }
    }
    
    handleCopy(buttonEl, content, iconName = 'copy') {
        if (!navigator.clipboard) {
            this.showToast('复制失败: 浏览器不支持或页面非HTTPS', 'error');
            return;
        }
        navigator.clipboard.writeText(content).then(() => {
            this.showToast('已复制到剪贴板', 'success');
            const icon = buttonEl.querySelector('i');
            if (icon) {
                icon.dataset.lucide = 'check';
                lucide.createIcons();
                setTimeout(() => {
                    icon.dataset.lucide = iconName;
                    lucide.createIcons();
                }, 2000);
            }
        }, (err) => {
            this.showToast('复制失败', 'error');
            console.error('复制失败:', err);
        });
    }

    toggleSearch() {
        this.state.isSearchEnabled = !this.state.isSearchEnabled;
        const { searchToggleBtn, searchOptions } = this.dom;
        searchToggleBtn.dataset.active = this.state.isSearchEnabled;
        searchToggleBtn.title = `网络搜索 (${this.state.isSearchEnabled ? '开启' : '关闭'})`;
        searchOptions.classList.toggle('hidden', !this.state.isSearchEnabled);
        searchOptions.classList.toggle('flex', this.state.isSearchEnabled);
    }
    
    toggleMcpOptions(enable) {
        const { mcpToggleBtn, mcpOptions } = this.dom;
        mcpToggleBtn.dataset.active = enable;
        mcpToggleBtn.title = `MCP模型上下文协议 (${enable ? '开启' : '关闭'})`;
        mcpOptions.classList.toggle('hidden', !enable);
        mcpOptions.classList.toggle('flex', enable);
    }

    setupLayoutControls() {
        this.dom.layoutControls.innerHTML = `
            <button data-layout="columns" class="p-1.5 rounded-md" title="分列视图"><i data-lucide="columns" class="w-4 h-4 pointer-events-none"></i></button>
            <button data-layout="rows" class="p-1.5 rounded-md" title="分行视图"><i data-lucide="rows" class="w-4 h-4 pointer-events-none"></i></button>
        `;
        const updateButtons = () => {
            this.dom.layoutControls.querySelectorAll('button').forEach(btn => {
                btn.classList.toggle('bg-blue-600', btn.dataset.layout === this.state.currentLayout);
                btn.classList.toggle('hover:bg-gray-700', btn.dataset.layout !== this.state.currentLayout);
            });
        };
        this.dom.layoutControls.addEventListener('click', (e) => {
            const btn = e.target.closest('button');
            if (btn) {
                this.state.currentLayout = btn.dataset.layout;
                updateButtons();
                this.renderChatGrid();
            }
        });
        updateButtons();
    }
    
    adjustTextareaHeight() {
        const textarea = this.dom.chatInput;
        textarea.style.height = 'auto';
        const newHeight = Math.min(textarea.scrollHeight, 200);
        textarea.style.height = `${newHeight}px`;
    }

    addCodeButtons(parentElement) {
        parentElement.querySelectorAll('pre').forEach(pre => {
            if (pre.querySelector('.code-controls')) return;
            const controls = this.createElement('div', {
                className: 'code-controls absolute top-2 right-2 flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity',
                innerHTML: `
                    <button class="code-copy-btn p-1.5 rounded-md bg-gray-600/50 hover:bg-gray-500/50" title="复制源码">
                        <i data-lucide="copy" class="w-4 h-4 text-gray-300 pointer-events-none"></i>
                    </button>`
            });
            pre.appendChild(controls);
        });
    }

    async handleShowAnnouncement() {
        try {
            const response = await fetch('./proxy.php?action=get_announcement');
            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error('公告文件 (announcement.md) 未在服务器上找到。');
                }
                throw new Error(`服务器错误，状态码: ${response.status}`);
            }
            const markdownContent = await response.text();
            this.openModal(new AnnouncementModal(this, markdownContent));
        } catch (error) {
            console.error('获取公告失败:', error);
            this.showToast(`无法加载公告: ${error.message}`, 'error');
        }
    }

    // --- 工具方法 ---
    escapeHtml = (unsafe) => unsafe.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
    sanitizeForId = (id) => id.replace(/[^a-zA-Z0-9-_]/g, '-');
    createElement = (tag, props = {}) => {
        const el = document.createElement(tag);
        for (const key in props) {
            if (key === 'dataset') {
                for (const dataKey in props[key]) {
                    el.dataset[dataKey] = props[key][dataKey];
                }
            } else {
                el[key] = props[key];
            }
        }
        return el;
    };
    getIconHtml = (icon, classes) => !icon ? '' : (icon.startsWith('http') || icon.startsWith('./') || icon.startsWith('data:')) ? `<img src="${this.escapeHtml(icon)}" class="${classes}" alt="icon">` : `<i data-lucide="${this.escapeHtml(icon)}" class="${classes}"></i>`;

    // --- 弹窗和提示 ---
    openModal(modalInstance) {
        this.dom.modalContainer.innerHTML = '';
        this.dom.modalContainer.appendChild(modalInstance.render());
        lucide.createIcons(); // **FIX**: Render icons AFTER the modal is in the DOM.
        modalInstance.show();
    }

    closeModal(modalInstance) {
        modalInstance.hide(() => {
            if (this.dom.modalContainer.contains(modalInstance.element)) {
                this.dom.modalContainer.removeChild(modalInstance.element);
            }
        });
    }

    showToast(message, type = 'info') {
        const existingToast = document.querySelector('.toast-notification');
        if (existingToast) {
            existingToast.remove();
        }

        const colors = {
            info: 'bg-blue-500',
            success: 'bg-green-500',
            warning: 'bg-yellow-500',
            error: 'bg-red-500',
        };
        const toast = this.createElement('div', {
            className: `toast-notification fixed bottom-5 right-5 text-white px-4 py-2 rounded-lg shadow-lg ${colors[type]} slide-in-up`,
            textContent: message
        });
        document.body.appendChild(toast);
        setTimeout(() => {
            toast.style.animation = 'fadeOut 0.5s ease-out forwards';
            setTimeout(() => toast.remove(), 500);
        }, 3000);
    }

    // --- MCP相关方法 ---
    renderMcpSelection() {
        const { mcpSelectionArea } = this.dom;
        mcpSelectionArea.innerHTML = '';

        if (this.state.mcpServers.length === 0) {
            mcpSelectionArea.innerHTML = '<span class="text-gray-500 text-sm">无可用MCP服务</span>';
            return;
        }

        this.state.mcpServers.forEach(srv => {
            const isChecked = this.state.mcpServersSelected.includes(srv.key);
            const label = this.createElement('label', {
                className: `flex items-center gap-1 px-2 py-1 rounded-full text-xs cursor-pointer transition-colors duration-200
                            ${isChecked ? 'bg-blue-600 text-white' : 'bg-gray-700 hover:bg-gray-600 text-gray-300'} `,
                innerHTML: `
                    <input type="checkbox" name="mcp-server" value="${srv.key}" ${isChecked ? 'checked' : ''} class="hidden">
                    <i data-lucide="server-cog" class="w-3 h-3"></i>
                    <span>${srv.key}</span>
                `
            });
            mcpSelectionArea.appendChild(label);
        });
        lucide.createIcons();
    }

    async fetchMcpServers() {
        try {
            const res = await fetch('./mcp_proxy.php?action=list');
            if (!res.ok) {
                throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }
            const data = await res.json();
            console.log('MCP服务器列表:', data);
            this.state.mcpServers = Object.entries(data).map(([key, v]) => ({ key, ...v }));
            this.renderMcpSelection();
        } catch (error) {
            console.error("获取MCP服务器列表失败:", error);
            this.showToast('无法获取MCP服务器列表', 'error');
        }
    }

    startMcpSse(serverKey, userText, modelId, userMessageIndex) {
        // **MODIFIED**: Reworked to add/update a message and display content.
        const mcpMessageId = `mcp-${serverKey}-${Date.now()}`;
        const mcpMessage = {
            role: 'mcp_status',
            id: mcpMessageId,
            serverKey: serverKey,
            status: 'loading',
            content: '' // Add content property
        };

        this.state.messages[modelId].splice(userMessageIndex + 1, 0, mcpMessage);
        this.renderMessages(modelId);

        const es = new EventSource(`./mcp_proxy.php?action=sse&server=${encodeURIComponent(serverKey)}`);
        let sessionId = null;
        let hasPosted = false;

        const updateMcpMessage = (newStatus, newContent = null) => {
            const msgIndex = this.state.messages[modelId].findIndex(m => m.id === mcpMessageId);
            if (msgIndex > -1) {
                this.state.messages[modelId][msgIndex].status = newStatus;
                if (newContent !== null) {
                    this.state.messages[modelId][msgIndex].content = newContent;
                }
                this.renderMessages(modelId);
            }
        };

        es.onmessage = (e) => {
            console.log('MCP SSE Message received:', e.data);

            if (!sessionId) {
                // Try to extract sessionId from URL format
                try {
                    const url = new URL(e.data, window.location.origin);
                    const params = new URLSearchParams(url.search);
                    if (params.has('sessionId')) sessionId = params.get('sessionId');
                    if (!sessionId && params.has('session_id')) sessionId = params.get('session_id');
                } catch (urlError) {
                    // Try regex patterns for sessionId
                    let match = e.data.match(/sessionId=([a-zA-Z0-9\-_]+)/i);
                    if (!match) match = e.data.match(/session_id=([a-zA-Z0-9\-_]+)/i);
                    if (match) sessionId = match[1];
                }

                // Try JSON parsing
                if (!sessionId) {
                    try {
                        const data = JSON.parse(e.data);
                        if (data.sessionId) sessionId = data.sessionId;
                        if (!sessionId && data.session_id) sessionId = data.session_id;
                    } catch {}
                }

                // Try simple colon format
                if (!sessionId && e.data.startsWith('sessionId:')) sessionId = e.data.split(':')[1].trim();
                if (!sessionId && e.data.startsWith('session_id:')) sessionId = e.data.split(':')[1].trim();

                // Try general pattern matching
                if (!sessionId) {
                    const match = e.data.match(/session[_-]?id[":=\/_\s]+([a-zA-Z0-9\-_]+)/i);
                    if (match) sessionId = match[1];
                }

                if (sessionId) {
                    console.log('MCP SessionId extracted:', sessionId);
                }
            }

            if (sessionId && !hasPosted) {
                hasPosted = true;
                console.log(`MCP POST request starting for ${serverKey} with sessionId: ${sessionId}`);

                fetch(`./mcp_proxy.php?action=post&server=${encodeURIComponent(serverKey)}&sessionId=${encodeURIComponent(sessionId)}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ url: userText })
                })
                .then(response => {
                    console.log(`MCP POST response status: ${response.status}`);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.text();
                })
                .then(responseText => {
                    console.log(`MCP POST response for ${serverKey}:`, responseText);
                    if (responseText && responseText.trim()) {
                        updateMcpMessage('complete', responseText);
                    } else {
                        updateMcpMessage('complete', '服务器返回了空响应');
                    }
                    es.close();
                })
                .catch(error => {
                    console.error(`MCP POST request error for ${serverKey}:`, error);
                    updateMcpMessage('error', `POST Error: ${error.message}`);
                    es.close();
                });
            }
        };

        es.onerror = (error) => {
            console.error(`MCP SSE connection error for ${serverKey}:`, error);
            const msgIndex = this.state.messages[modelId].findIndex(m => m.id === mcpMessageId);
            if (msgIndex > -1 && this.state.messages[modelId][msgIndex].status === 'loading') {
                 updateMcpMessage('error', `与MCP服务器 ${serverKey} 的连接失败。请检查网络连接和服务器状态。`);
            }
            es.close();
        };

        es.onopen = () => {
            console.log(`MCP SSE connection opened for ${serverKey}`);
        };
    }

    // --- 快捷短语及参数设置相关方法 ---
    toggleQuickPhrasesPopover() {
        if (this.quickPhrasesPopover) {
            this.closeQuickPhrasesPopover();
            return;
        }
        this.closeParameterPopover();
        this.closeSourcesPopover();
        const btnRect = this.dom.quickPhrasesBtn.getBoundingClientRect();
        this.quickPhrasesPopover = this.createElement('div', {
            className: 'fixed z-10 w-64 bg-gray-800 border border-gray-700 rounded-lg shadow-lg text-white',
            style: `top: ${btnRect.top}px; left: ${btnRect.left}px; transform: translateY(-100%) translateY(-10px);`
        });

        this.renderQuickPhrasesPopoverContent();
        document.body.appendChild(this.quickPhrasesPopover);
        lucide.createIcons();
        
        setTimeout(() => {
            document.addEventListener('click', this.handleClickOutsidePopover, true);
        }, 0);
    }

    closeQuickPhrasesPopover = () => {
        if (this.quickPhrasesPopover) {
            this.quickPhrasesPopover.remove();
            this.quickPhrasesPopover = null;
            document.removeEventListener('click', this.handleClickOutsidePopover, true);
        }
    }

    toggleParameterPopover() {
        if (this.parameterPopover) {
            this.closeParameterPopover();
            return;
        }
        this.closeQuickPhrasesPopover();
        this.closeSourcesPopover();
        const btnRect = this.dom.parameterSettingsBtn.getBoundingClientRect();
        this.parameterPopover = this.createElement('div', {
            className: 'fixed z-10 w-72 bg-gray-800 border border-gray-700 rounded-lg shadow-lg text-white p-4 space-y-4',
            style: `top: ${btnRect.top}px; left: ${btnRect.left}px; transform: translateY(-100%) translateY(-10px);`
        });

        this.renderParameterPopoverContent();
        document.body.appendChild(this.parameterPopover);
        lucide.createIcons();
        
        setTimeout(() => {
            document.addEventListener('click', this.handleClickOutsidePopover, true);
        }, 0);
    }

    closeParameterPopover = () => {
        if (this.parameterPopover) {
            localStorage.setItem('chathub-temperature', this.state.temperature);
            localStorage.setItem('chathub-contextLength', this.state.contextLength);
            this.parameterPopover.remove();
            this.parameterPopover = null;
            document.removeEventListener('click', this.handleClickOutsidePopover, true);
        }
    }

    toggleSourcesPopover(button) {
        if (this.sourcesPopover) {
            this.closeSourcesPopover();
            if (this.sourcesPopover.dataset.messageId === `${button.dataset.modelId}-${button.dataset.messageIndex}`) {
                return;
            }
        }
        this.closeQuickPhrasesPopover();
        this.closeParameterPopover();

        const modelId = button.dataset.modelId;
        const messageIndex = parseInt(button.dataset.messageIndex, 10);
        const sources = this.state.messages[modelId]?.[messageIndex]?.sources;

        if (!sources || sources.length === 0) return;

        const btnRect = button.getBoundingClientRect();
        this.sourcesPopover = this.createElement('div', {
            className: 'fixed z-10 w-80 bg-gray-800 border border-gray-700 rounded-lg shadow-lg text-white p-2 space-y-2',
            style: `top: ${btnRect.top}px; left: ${btnRect.left}px; transform: translateY(-100%) translateY(-10px);`,
            dataset: { messageId: `${modelId}-${messageIndex}` }
        });
        
        let content = '<div class="max-h-60 overflow-y-auto custom-scrollbar p-1 space-y-2">';
        sources.forEach((source, i) => {
            content += `
                <a href="${this.escapeHtml(source.url)}" target="_blank" rel="noopener noreferrer" class="block p-2 rounded-md hover:bg-gray-700">
                    <div class="text-sm font-semibold text-blue-400 truncate">[${i+1}] ${this.escapeHtml(source.title)}</div>
                    <p class="text-xs text-gray-400 mt-1 line-clamp-2">${this.escapeHtml(source.content)}</p>
                </a>
            `;
        });
        content += '</div>';

        this.sourcesPopover.innerHTML = content;
        document.body.appendChild(this.sourcesPopover);

        setTimeout(() => {
            document.addEventListener('click', this.handleClickOutsidePopover, true);
        }, 0);
    }

    closeSourcesPopover = () => {
        if (this.sourcesPopover) {
            this.sourcesPopover.remove();
            this.sourcesPopover = null;
            document.removeEventListener('click', this.handleClickOutsidePopover, true);
        }
    }
    
    handleClickOutsidePopover = (e) => {
        if (this.quickPhrasesPopover && !this.quickPhrasesPopover.contains(e.target) && !this.dom.quickPhrasesBtn.contains(e.target)) {
            this.closeQuickPhrasesPopover();
        }
        if (this.parameterPopover && !this.parameterPopover.contains(e.target) && !this.dom.parameterSettingsBtn.contains(e.target)) {
            this.closeParameterPopover();
        }
        if (this.sourcesPopover && !this.sourcesPopover.contains(e.target) && !e.target.closest('.show-sources-btn')) {
            this.closeSourcesPopover();
        }
    }

    renderParameterPopoverContent() {
        if (!this.parameterPopover) return;
        this.parameterPopover.innerHTML = `
            <div class="space-y-4 text-sm text-gray-300">
                <div>
                    <div class="flex items-center justify-between mb-1">
                        <label for="popover-temperature-slider" class="flex items-center gap-1.5"><i data-lucide="thermometer" class="w-4 h-4"></i>温度</label>
                        <span id="popover-temperature-value" class="text-white font-mono w-10 text-right">${this.state.temperature.toFixed(1)}</span>
                    </div>
                    <input id="popover-temperature-slider" type="range" min="0" max="2" value="${this.state.temperature}" step="0.1" class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                </div>
                <div>
                    <div class="flex items-center justify-between mb-1">
                        <label for="popover-context-slider" class="flex items-center gap-1.5"><i data-lucide="history" class="w-4 h-4"></i>上下文</label>
                        <span id="popover-context-value" class="text-white font-mono w-10 text-right">${this.state.contextLength}</span>
                    </div>
                    <input id="popover-context-slider" type="range" min="0" max="20" value="${this.state.contextLength}" step="1" class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                </div>
            </div>
        `;
        lucide.createIcons();

        const tempSlider = this.parameterPopover.querySelector('#popover-temperature-slider');
        const tempValue = this.parameterPopover.querySelector('#popover-temperature-value');
        const contextSlider = this.parameterPopover.querySelector('#popover-context-slider');
        const contextValue = this.parameterPopover.querySelector('#popover-context-value');

        tempSlider.addEventListener('input', (e) => {
            this.state.temperature = parseFloat(e.target.value);
            tempValue.textContent = this.state.temperature.toFixed(1);
        });
        contextSlider.addEventListener('input', (e) => {
            this.state.contextLength = parseInt(e.target.value, 10);
            contextValue.textContent = this.state.contextLength;
        });
    }

    renderQuickPhrasesPopoverContent() {
        if (!this.quickPhrasesPopover) return;
        
        let content = '<div class="p-2 max-h-48 overflow-y-auto custom-scrollbar">';
        if (this.state.quickPhrases.length > 0) {
            this.state.quickPhrases.forEach(phrase => {
                content += `
                    <button class="quick-phrase-item w-full text-left p-2 rounded-md hover:bg-gray-700 text-sm truncate">
                        ${this.escapeHtml(phrase)}
                    </button>`;
            });
        } else {
            content += '<p class="text-gray-500 text-sm text-center p-4">无快捷短语</p>';
        }
        content += '</div>';

        content += `
            <div class="p-2 border-t border-gray-700/50">
                <button class="manage-quick-phrases-btn w-full flex items-center justify-center gap-2 p-2 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors font-medium text-sm">
                    <i data-lucide="settings-2" class="w-4 h-4"></i>
                    管理短语
                </button>
            </div>`;
        
        this.quickPhrasesPopover.innerHTML = content;
        lucide.createIcons();

        this.quickPhrasesPopover.querySelectorAll('.quick-phrase-item').forEach(btn => {
            btn.addEventListener('click', () => {
                this.dom.chatInput.value = btn.textContent.trim();
                this.dom.chatInput.focus();
                this.adjustTextareaHeight();
                this.closeQuickPhrasesPopover();
            });
        });

        this.quickPhrasesPopover.querySelector('.manage-quick-phrases-btn').addEventListener('click', () => {
            this.closeQuickPhrasesPopover();
            this.openModal(new QuickPhrasesModal(this));
        });
    }
}

// --- 模态框基类 ---
class Modal {
    constructor(app, id) {
        this.app = app;
        this.id = id;
        this.element = null;
    }
    render() { /* to be implemented by subclass */ }
    
    show() {
        const panel = this.element.querySelector('.modal-panel');
        this.element.classList.remove('modal-enter');
        panel.classList.remove('modal-panel-enter');
        this.element.classList.add('modal-enter-active');
        panel.classList.add('modal-panel-enter-active');
        this.attachEventListeners();
    }

    hide(onClose) {
        const panel = this.element.querySelector('.modal-panel');
        this.element.classList.remove('modal-enter-active');
        panel.classList.remove('modal-panel-enter-active');
        this.element.classList.add('modal-leave-active');
        panel.classList.add('modal-panel-leave-active');
        setTimeout(onClose, 200);
    }

    close = () => {
        this.app.closeModal(this);
    }
    
    attachEventListeners() {
        this.element.querySelector('.modal-close-btn')?.addEventListener('click', this.close);
        this.element.addEventListener('click', (e) => {
            if (e.target === this.element) this.close();
        });
    }
}

// --- 设置模态框 ---
class SettingsModal extends Modal {
    constructor(app) {
        super(app, 'settings-modal');
    }

    render() {
        this.element = this.app.createElement('div', {
            id: this.id,
            className: 'modal-enter fixed inset-0 z-40 bg-black/50 flex items-center justify-center p-4',
            innerHTML: `
                <div class="modal-panel modal-panel-enter bg-gray-800 rounded-2xl shadow-xl w-full max-w-3xl h-[85vh] flex flex-col">
                    <header class="flex items-center justify-between p-4 border-b border-gray-700 flex-shrink-0">
                        <h2 class="text-lg font-semibold">模型设置</h2>
                        <button class="modal-close-btn p-1.5 rounded-full bg-gray-900/50 hover:bg-gray-700/80 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i data-lucide="x" class="w-5 h-5"></i>
                        </button>
                    </header>
                    <main id="settings-list" class="flex-1 p-4 overflow-y-auto custom-scrollbar space-y-3"></main>
                    <footer class="p-4 border-t border-gray-700 flex-shrink-0 flex justify-between items-center">
                        <p class="text-xs text-gray-500 flex items-center gap-2"><i data-lucide="info" class="w-4 h-4"></i>模型配置保存在浏览器本地，不会上传。</p>
                        <button id="add-model-btn" class="px-4 py-2 bg-blue-600 rounded-lg hover:bg-blue-700 font-semibold">添加新模型</button>
                    </footer>
                </div>`
        });
        this.renderModelSettings();
        return this.element;
    }

    renderModelSettings() {
        const container = this.element.querySelector('#settings-list');
        container.innerHTML = '';
        
        const userModels = this.app.state.models.filter(model => !model.isPreset);
        const presetModels = this.app.state.models.filter(model => model.isPreset);
    
        if (userModels.length > 0) {
            container.innerHTML += `<h3 class="text-sm font-semibold text-gray-400 px-1 pt-2">自定义模型</h3>`;
            this.app.state.models.forEach((model, index) => {
                if (model.isPreset) return;

                const item = this.app.createElement('div', {
                    className: 'p-4 bg-gray-700/50 rounded-lg border border-gray-600',
                    innerHTML: `
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <input type="text" data-index="${index}" data-field="name" value="${this.app.escapeHtml(model.name)}" placeholder="模型名称" class="bg-gray-800 p-2 rounded-md border border-gray-600 focus:ring-2 focus:ring-blue-500 outline-none">
                            <input type="text" data-index="${index}" data-field="id" value="${this.app.escapeHtml(model.id)}" placeholder="模型 ID" class="bg-gray-800 p-2 rounded-md border border-gray-600 focus:ring-2 focus:ring-blue-500 outline-none md:col-span-2">
                            <input type="text" data-index="${index}" data-field="api_url" value="${this.app.escapeHtml(model.api_url)}" placeholder="API URL, 例如: https://api.openai.com/v1/chat/completions" class="bg-gray-800 p-2 rounded-md border border-gray-600 focus:ring-2 focus:ring-blue-500 outline-none md:col-span-2">
                            <input type="password" data-index="${index}" data-field="api_key" value="${this.app.escapeHtml(model.api_key)}" placeholder="API Key" class="bg-gray-800 p-2 rounded-md border border-gray-600 focus:ring-2 focus:ring-blue-500 outline-none md:col-span-2">
                            <input type="text" data-index="${index}" data-field="icon" value="${this.app.escapeHtml(model.icon)}" placeholder="图标 (URL 或 Lucide 名称)" class="bg-gray-800 p-2 rounded-md border border-gray-600 focus:ring-2 focus:ring-blue-500 outline-none">
                            <div class="flex items-center justify-between">
                                <label class="flex items-center gap-2 text-sm cursor-pointer">
                                    <input type="checkbox" data-index="${index}" data-field="search" ${(model.search || model.supports_search) ? 'checked' : ''} class="w-4 h-4 rounded text-blue-500 bg-gray-800 border-gray-600 focus:ring-blue-600">
                                    支持搜索
                                </label>
                                <label class="flex items-center gap-2 text-sm cursor-pointer">
                                    <input type="checkbox" data-index="${index}" data-field="supports_vision" ${model.supports_vision ? 'checked' : ''} class="w-4 h-4 rounded text-blue-500 bg-gray-800 border-gray-600 focus:ring-blue-600">
                                    支持视觉
                                </label>
                                <button data-index="${index}" class="remove-model-btn text-red-400 hover:text-red-300 p-1 rounded-md"><i data-lucide="trash-2" class="w-4 h-4 pointer-events-none"></i></button>
                            </div>
                        </div>`
                });
                container.appendChild(item);
            });
        }
    
        if (presetModels.length > 0) {
            container.innerHTML += `<h3 class="text-sm font-semibold text-gray-400 px-1 pt-4">预设模型</h3>`;
            presetModels.forEach(model => {
                const iconHtml = this.app.getIconHtml(model.icon, 'w-5 h-5 mr-3');
                const visionIcon = model.supports_vision ? '<i data-lucide="camera" class="w-4 h-4 text-blue-400 ml-2" title="支持视觉"></i>' : '';
                const item = this.app.createElement('div', {
                    className: 'p-3 bg-gray-700/50 rounded-lg border border-gray-600 flex items-center justify-between',
                    innerHTML: `
                        <div class="flex items-center">
                            ${iconHtml}
                            <span class="text-sm">${this.app.escapeHtml(model.name)}</span>
                            ${visionIcon}
                        </div>
                        <span class="text-xs text-gray-500">预设模型无法修改</span>
                    `
                });
                container.appendChild(item);
            });
        }
        
        if (userModels.length === 0) {
             container.innerHTML += `<div class="text-center text-gray-500 py-10">没有自定义模型。请点击下方按钮添加您的第一个模型。</div>`;
        }
    
    }

    attachEventListeners() {
        super.attachEventListeners();
        const container = this.element;
        container.querySelector('#add-model-btn').addEventListener('click', () => this.addModel());
        
        const settingsList = container.querySelector('#settings-list');

        settingsList.addEventListener('input', (e) => {
            const target = e.target;
            const index = target.dataset.index;
            if (index === undefined) return;
            
            const field = target.dataset.field;
            const value = target.type === 'checkbox' ? target.checked : target.value;
            this.app.state.models[index][field] = value;
            this.app.saveModelsToLocalStorage();
            this.app.renderModelList();
        });

        settingsList.addEventListener('click', (e) => {
            const removeBtn = e.target.closest('.remove-model-btn');
            if (removeBtn) {
                this.removeModel(removeBtn.dataset.index);
            }
        });
    }

    addModel() {
        this.app.state.models.push({
            id: `new-model-${Date.now()}`, name: '新模型', api_url: '', api_key: '',
            enabled: true, search: false, icon: 'bot', supports_vision: false,
            isPreset: false
        });
        this.renderModelSettings();
        lucide.createIcons(); // Re-render icons after adding new elements
    }
    
    removeModel(index) {
        if (this.app.state.models[index] && !this.app.state.models[index].isPreset) {
            this.app.state.models.splice(index, 1);
            this.app.saveModelsToLocalStorage();
            this.app.renderAll();
            this.renderModelSettings();
            lucide.createIcons(); // Re-render icons
        }
    }
}


// --- 历史记录模态框 ---
class HistoryModal extends Modal {
    constructor(app, modelId) {
        super(app, 'history-modal');
        this.modelId = modelId;
        this.searchTerm = '';
    }
    
    render() {
        const model = this.app.state.models.find(m => m.id === this.modelId);
        this.element = this.app.createElement('div', {
            id: this.id,
            className: 'modal-enter fixed inset-0 z-40 bg-black/50 flex items-center justify-center p-4',
            innerHTML: `
                <div class="modal-panel modal-panel-enter bg-gray-800 rounded-2xl shadow-xl w-full max-w-2xl h-[80vh] flex flex-col">
                    <header class="flex items-center justify-between p-4 border-b border-gray-700 flex-shrink-0">
                        <h2 class="text-lg font-semibold">与 ${this.app.escapeHtml(model?.name || '')} 的聊天记录</h2>
                        <div class="flex items-center gap-2">
                            <button id="history-clear-all-btn" class="text-xs p-1.5 rounded-md text-red-400 hover:bg-red-500/10" title="全部清除">全部清除</button>
                            <button class="modal-close-btn p-1.5 rounded-full hover:bg-gray-700"><i data-lucide="x" class="w-5 h-5"></i></button>
                        </div>
                    </header>
                    <div class="p-4 border-b border-gray-700 flex-shrink-0">
                        <div class="relative"><i data-lucide="search" class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-500"></i>
                            <input id="history-search" type="text" placeholder="搜索记录..." class="w-full bg-gray-700 border border-gray-600 rounded-lg p-3 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <main id="history-list" class="flex-1 p-4 overflow-y-auto custom-scrollbar space-y-2"></main>
                    <footer class="p-3 text-center border-t border-gray-700 flex-shrink-0">
                        <p class="text-xs text-gray-500 flex items-center justify-center gap-2"><i data-lucide="lock" class="w-3 h-3"></i>聊天记录保存在本地浏览器</p>
                    </footer>
                </div>`
        });
        this.renderHistoryList();
        return this.element;
    }

    renderHistoryList() {
        const listEl = this.element.querySelector('#history-list');
        listEl.innerHTML = '';
        
        const activeConv = this.app.state.messages[this.modelId];
        let combinedList = [];
        if (activeConv && activeConv.length > 0) {
            combinedList.push({ id: 'active', title: activeConv[0].content, messages: activeConv, isActive: true });
        }
        combinedList = combinedList.concat(this.app.state.history[this.modelId] || []);
        
        const filteredList = this.searchTerm
            ? combinedList.filter(c => c.title.toLowerCase().includes(this.searchTerm.toLowerCase()))
            : combinedList;
        
        if (filteredList.length === 0) {
            listEl.innerHTML = `<div class="text-center text-gray-500 py-10">没有历史记录。</div>`;
            return;
        }

        filteredList.forEach(conv => {
            const item = this.app.createElement('div', {
                className: `history-item group flex items-center justify-between p-3 rounded-lg border cursor-pointer ${conv.isActive ? 'border-blue-500/50 bg-gray-700/50' : 'border-transparent hover:bg-gray-700/50'} `,
                dataset: { historyId: conv.id },
            });
            
            item.innerHTML = `
                <div class="flex-1 truncate pr-4">${this.app.escapeHtml(conv.title.substring(0, 50))}${conv.isActive ? '<span class="text-xs text-blue-400 font-semibold ml-2">[当前]</span>' : ''}</div>
                ${!conv.isActive ? `<button class="delete-history-btn p-1.5 rounded-md hover:bg-red-500/20 opacity-0 group-hover:opacity-100"><i data-lucide="trash-2" class="w-4 h-4 text-red-400 pointer-events-none"></i></button>` : ''}`;
            
            listEl.appendChild(item);
        });
        lucide.createIcons();
    }
    
    attachEventListeners() {
        super.attachEventListeners();
        const container = this.element;
        container.querySelector('#history-search').addEventListener('input', (e) => {
            this.searchTerm = e.target.value;
            this.renderHistoryList();
        });
        container.querySelector('#history-clear-all-btn').addEventListener('click', () => this.clearAllHistory());
        
        container.querySelector('#history-list').addEventListener('click', (e) => {
            const item = e.target.closest('.history-item');
            if (!item) return;
            
            if (e.target.closest('.delete-history-btn')) {
                e.stopPropagation();
                this.deleteHistoryItem(item.dataset.historyId);
            } else {
                this.loadHistoryItem(item.dataset.historyId);
            }
        });
    }

    clearAllHistory() {
        if (confirm('您确定要清除此模型的所有历史记录吗？(当前对话不会被清除)')) {
            this.app.state.history[this.modelId] = [];
            this.app.saveConversations();
            this.renderHistoryList();
        }
    }
    
    deleteHistoryItem(historyId) {
        if (historyId === 'active') return;
        this.app.state.history[this.modelId] = this.app.state.history[this.modelId].filter(c => c.id.toString() !== historyId);
        this.app.saveConversations();
        this.renderHistoryList();
    }
    
    loadHistoryItem(historyId) {
        if (historyId === 'active') { this.close(); return; }

        if(this.app.controllers[this.modelId]) {
            this.app.controllers[this.modelId].abort();
        }
        
        const convToLoad = this.app.state.history[this.modelId].find(c => c.id.toString() === historyId);
        if (!convToLoad) return;
        
        const currentMessages = this.app.state.messages[this.modelId];
        if (currentMessages && currentMessages.length > 0) {
             const conversationToArchive = {
                id: Date.now() + Math.random(),
                title: currentMessages[0].content,
                messages: JSON.parse(JSON.stringify(currentMessages))
            };
            if (!this.app.state.history[this.modelId]) {
                this.app.state.history[this.modelId] = [];
            }
            this.app.state.history[this.modelId].unshift(conversationToArchive);
        }
        
        this.app.state.messages[this.modelId] = JSON.parse(JSON.stringify(convToLoad.messages));
        this.app.state.history[this.modelId] = this.app.state.history[this.modelId].filter(c => c.id.toString() !== historyId);
        
        this.app.saveConversations();
        this.app.renderMessages(this.modelId);
        this.close();
    }
}

// --- 快捷短语模态框 ---
class QuickPhrasesModal extends Modal {
    constructor(app) {
        super(app, 'quick-phrases-modal');
    }

    render() {
        this.element = this.app.createElement('div', {
            id: this.id,
            className: 'modal-enter fixed inset-0 z-40 bg-black/50 flex items-center justify-center p-4',
            innerHTML: `
                <div class="modal-panel modal-panel-enter bg-gray-800 rounded-2xl shadow-xl w-full max-w-2xl h-[80vh] flex flex-col">
                    <header class="flex items-center justify-between p-4 border-b border-gray-700 flex-shrink-0">
                        <h2 class="text-lg font-semibold">管理快捷短语</h2>
                        <button class="modal-close-btn p-1.5 rounded-full hover:bg-gray-700"><i data-lucide="x" class="w-5 h-5"></i></button>
                    </header>
                    <main id="quick-phrases-list" class="flex-1 p-4 overflow-y-auto custom-scrollbar space-y-3"></main>
                    <footer class="p-4 border-t border-gray-700 flex-shrink-0">
                        <form id="add-phrase-form" class="flex items-center gap-3">
                            <input id="add-phrase-input" type="text" placeholder="输入新短语..." class="flex-1 bg-gray-700 border border-gray-600 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <button type="submit" class="px-4 py-2 bg-blue-600 rounded-lg hover:bg-blue-700 font-semibold flex items-center gap-2">
                                <i data-lucide="plus" class="w-4 h-4"></i> 添加
                            </button>
                        </form>
                    </footer>
                </div>`
        });
        this.renderPhraseList();
        return this.element;
    }

    renderPhraseList() {
        const listEl = this.element.querySelector('#quick-phrases-list');
        listEl.innerHTML = '';

        if (this.app.state.quickPhrases.length === 0) {
            listEl.innerHTML = `<div class="text-center text-gray-500 py-10">还没有快捷短语，在下方添加一个吧。</div>`;
            return;
        }

        this.app.state.quickPhrases.forEach((phrase, index) => {
            const item = this.app.createElement('div', {
                className: 'phrase-item group flex items-center gap-3 p-2 bg-gray-700/50 rounded-lg',
                innerHTML: `
                    <i data-lucide="message-square" class="w-5 h-5 text-gray-400 flex-shrink-0"></i>
                    <input type="text" value="${this.app.escapeHtml(phrase)}" data-index="${index}" class="phrase-input flex-1 bg-transparent outline-none focus:bg-gray-700 p-1 rounded-md">
                    <button data-index="${index}" class="delete-phrase-btn p-1.5 rounded-md hover:bg-red-500/20 opacity-0 group-hover:opacity-100">
                        <i data-lucide="trash-2" class="w-4 h-4 text-red-400 pointer-events-none"></i>
                    </button>
                `
            });
            listEl.appendChild(item);
        });
        lucide.createIcons();
    }

    attachEventListeners() {
        super.attachEventListeners();
        const container = this.element;

        container.querySelector('#add-phrase-form').addEventListener('submit', (e) => {
            e.preventDefault();
            const input = container.querySelector('#add-phrase-input');
            const newPhrase = input.value.trim();
            if (newPhrase) {
                this.addPhrase(newPhrase);
                input.value = '';
            }
        });

        const listEl = container.querySelector('#quick-phrases-list');
        listEl.addEventListener('click', (e) => {
            const deleteBtn = e.target.closest('.delete-phrase-btn');
            if (deleteBtn) {
                this.deletePhrase(parseInt(deleteBtn.dataset.index));
            }
        });

        listEl.addEventListener('change', (e) => {
            const input = e.target.closest('.phrase-input');
            if (input) {
                this.updatePhrase(parseInt(input.dataset.index), input.value);
            }
        });
    }

    addPhrase(phrase) {
        this.app.state.quickPhrases.push(phrase);
        this.app.saveQuickPhrases();
        this.renderPhraseList();
    }

    deletePhrase(index) {
        this.app.state.quickPhrases.splice(index, 1);
        this.app.saveQuickPhrases();
        this.renderPhraseList();
    }

    updatePhrase(index, newText) {
        if (newText.trim()) {
            this.app.state.quickPhrases[index] = newText.trim();
        } else {
            this.app.state.quickPhrases.splice(index, 1);
        }
        this.app.saveQuickPhrases();
        this.renderPhraseList();
    }
}

// --- 数据管理模态框 ---
class DataManagementModal extends Modal {
    constructor(app) {
        super(app, 'data-management-modal');
    }

    render() {
        this.element = this.app.createElement('div', {
            id: this.id,
            className: 'modal-enter fixed inset-0 z-40 bg-black/50 flex items-center justify-center p-4',
            innerHTML: `
                <div class="modal-panel modal-panel-enter bg-gray-800 rounded-2xl shadow-xl w-full max-w-md flex flex-col">
                    <header class="flex items-center justify-between p-4 border-b border-gray-700 flex-shrink-0">
                        <h2 class="text-lg font-semibold">数据管理</h2>
                        <button class="modal-close-btn p-1.5 rounded-full hover:bg-gray-700"><i data-lucide="x" class="w-5 h-5"></i></button>
                    </header>
                    <main class="p-6 space-y-4">
                        <button id="clear-conversations-btn" class="w-full text-left p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                            <h3 class="font-semibold">清除对话记录</h3>
                            <p class="text-xs text-gray-400 mt-1">删除所有已保存的聊天和历史记录。</p>
                        </button>
                        <button id="clear-models-btn" class="w-full text-left p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                            <h3 class="font-semibold">清除自定义模型</h3>
                            <p class="text-xs text-gray-400 mt-1">删除所有您手动添加的模型配置。</p>
                        </button>
                        <button id="clear-phrases-btn" class="w-full text-left p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                            <h3 class="font-semibold">清除快捷短语</h3>
                            <p class="text-xs text-gray-400 mt-1">删除所有已保存的快捷短语。</p>
                        </button>
                        <div class="border-t border-gray-700 pt-4">
                            <button id="clear-all-btn" class="w-full p-3 bg-red-800/50 text-red-300 hover:bg-red-800/80 rounded-lg transition-colors">
                                <h3 class="font-semibold">清除所有数据</h3>
                                <p class="text-xs text-red-400 mt-1">警告：此操作将删除以上所有数据，无法恢复。</p>
                            </button>
                        </div>
                    </main>
                </div>`
        });
        return this.element;
    }

    attachEventListeners() {
        super.attachEventListeners();
        this.element.querySelector('#clear-conversations-btn').addEventListener('click', () => this.clearConversations());
        this.element.querySelector('#clear-models-btn').addEventListener('click', () => this.clearCustomModels());
        this.element.querySelector('#clear-phrases-btn').addEventListener('click', () => this.clearQuickPhrases());
        this.element.querySelector('#clear-all-btn').addEventListener('click', () => this.clearAll());
    }

    clearConversations() {
        if (confirm('您确定要清除所有聊天记录吗？此操作无法撤销。')) {
            localStorage.removeItem('chathub-messages');
            localStorage.removeItem('chathub-history');
            this.app.showToast('对话记录已清除，页面将刷新。', 'success');
            setTimeout(() => location.reload(), 1500);
        }
    }

    clearCustomModels() {
        if (confirm('您确定要清除所有自定义模型吗？预设模型将保留。此操作无法撤销。')) {
            const models = JSON.parse(localStorage.getItem('chathub-models') || '[]');
            const presetModels = models.filter(m => m.isPreset);
            localStorage.setItem('chathub-models', JSON.stringify(presetModels));
            this.app.showToast('自定义模型已清除，页面将刷新。', 'success');
            setTimeout(() => location.reload(), 1500);
        }
    }

    clearQuickPhrases() {
        if (confirm('您确定要清除所有快捷短语吗？此操作无法撤销。')) {
            localStorage.removeItem('chathub-quick-phrases');
            this.app.showToast('快捷短语已清除，页面将刷新。', 'success');
            setTimeout(() => location.reload(), 1500);
        }
    }
    
    clearAll() {
        if (confirm('警告！您确定要清除所有本地数据吗？这将包括所有自定义模型、聊天记录和快捷短语。此操作无法撤销。')) {
            localStorage.removeItem('chathub-models');
            localStorage.removeItem('chathub-messages');
            localStorage.removeItem('chathub-history');
            localStorage.removeItem('chathub-quick-phrases');
            localStorage.removeItem('chathub-mcp-enabled');
            localStorage.removeItem('chathub-mcp-servers');
            localStorage.removeItem('chathub-temperature');
            localStorage.removeItem('chathub-contextLength');
            this.app.showToast('所有数据已清除，页面将刷新。', 'success');
            setTimeout(() => location.reload(), 1500);
        }
    }
}

// --- 公告模态框 ---
class AnnouncementModal extends Modal {
    constructor(app, markdownContent) {
        super(app, 'announcement-modal');
        this.markdownContent = markdownContent;
    }

    render() {
        this.element = this.app.createElement('div', {
            id: this.id,
            className: 'modal-enter fixed inset-0 z-40 bg-black/50 flex items-center justify-center p-4',
            innerHTML: `
                <div class="modal-panel modal-panel-enter bg-gray-800 rounded-2xl shadow-xl w-full max-w-2xl h-auto max-h-[80vh] flex flex-col">
                    <header class="flex items-center justify-between p-4 border-b border-gray-700 flex-shrink-0">
                        <h2 class="text-lg font-semibold flex items-center gap-2"><i data-lucide="siren" class="w-5 h-5 text-yellow-400"></i>公告</h2>
                        <button class="modal-close-btn p-1.5 rounded-full hover:bg-gray-700"><i data-lucide="x" class="w-5 h-5"></i></button>
                    </header>
                    <main class="flex-1 p-6 overflow-y-auto custom-scrollbar prose prose-invert">
                        ${marked.parse(this.markdownContent)}
                    </main>
                </div>`
        });
        this.app.addCodeButtons(this.element);
        return this.element;
    }
}
