<?php

class McpProxyHandler {
    private $config;

    public function __construct() {
        $configPath = __DIR__ . '/mcp_config.json';
        if (!file_exists($configPath)) {
            // error_log("MCP Proxy Error: mcp_config.json not found at {$configPath}");
            http_response_code(500);
            exit('Configuration file not found');
        }
        $this->config = json_decode(file_get_contents($configPath), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            // error_log("MCP Proxy Error: Failed to parse mcp_config.json. Error: " . json_last_error_msg());
            http_response_code(500);
            exit('Configuration file parsing error');
        }
    }

    public function handleRequest() {
        // Add CORS headers
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Cache-Control');

        // Handle preflight requests
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }

        $action = $_GET['action'] ?? '';

        switch ($action) {
            case 'list':
                $this->handleList();
                break;
            case 'sse':
                $this->handleSse();
                break;
            case 'post':
                $this->handlePost();
                break;
            default:
                http_response_code(404);
                echo 'Invalid request';
                error_log("MCP Proxy: Invalid request received. Action: {$action}");
                exit;
        }
    }

    private function handleList() {
        header('Content-Type: application/json');
        echo json_encode($this->config['mcpServers'] ?? []);
        exit;
    }

    private function handleSse() {
        set_time_limit(0); // 允许脚本无限执行，以支持SSE长连接
        $server = $_GET['server'] ?? '';
        $url = $this->config['mcpServers'][$server]['url'] ?? null;

        if (!$url) {
            http_response_code(404);
            error_log("MCP Proxy SSE Error: Server '{$server}' not found or URL missing in config.");
            exit('Server not found');
        }

        error_log("MCP Proxy SSE: Attempting to connect to {$url}");

        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: Cache-Control');
        @ob_end_flush();
        @ob_implicit_flush(true);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_TIMEOUT, 300); // Set a longer timeout for SSE
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // Connection timeout
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For HTTPS issues
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1); // Force HTTP/1.1 to avoid HTTP/2 issues
        curl_setopt($ch, CURLOPT_USERAGENT, 'MCP-Proxy/1.0');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: text/event-stream',
            'Cache-Control: no-cache'
        ]);
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) {
            error_log("MCP Proxy SSE Data: " . trim($data));
            echo $data;
            @ob_flush();
            @flush();
            return strlen($data);
        });

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            error_log("MCP Proxy SSE cURL Error: " . curl_error($ch) . " (HTTP Code: {$httpCode})");
            echo "event: error\ndata: " . json_encode(['error' => curl_error($ch)]) . "\n\n";
        } else {
            error_log("MCP Proxy SSE cURL Finished. HTTP Code: {$httpCode}. Result: " . ($result ? "Success" : "Failure"));
        }
        curl_close($ch);
        exit;
    }

    private function handlePost() {
        $server = $_GET['server'] ?? '';
        $sessionId = $_GET['sessionId'] ?? '';
        $url = $this->config['mcpServers'][$server]['url'] ?? null;

        if (!$url) {
            http_response_code(404);
            error_log("MCP Proxy POST Error: Server '{$server}' not found or URL missing in config.");
            exit('Server not found');
        }
        if (empty($sessionId)) {
            http_response_code(400);
            error_log("MCP Proxy POST Error: sessionId is missing.");
            exit('Session ID missing');
        }

        // **FIXED**: Use a more generic regex to replace the last path segment with '/messages'
        // This correctly handles URLs like /fetch, /sequential, etc.
        $postUrl = preg_replace('/\/[^\/]+$/', '/messages', $url) . '?sessionId=' . urlencode($sessionId);
        $body = file_get_contents('php://input');

        error_log("MCP Proxy POST: Sending to {$postUrl} with sessionId {$sessionId}. Body: {$body}");

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $postUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'User-Agent: MCP-Proxy/1.0'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1); // Force HTTP/1.1

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            error_log("MCP Proxy POST cURL Error: " . curl_error($ch) . " (HTTP Code: {$httpCode})");
            http_response_code(500);
            echo json_encode(['error' => curl_error($ch)]);
        } else {
            error_log("MCP Proxy POST cURL Finished. HTTP Code: {$httpCode}. Result: {$result}");
            http_response_code($httpCode);
            echo $result;
        }
        curl_close($ch);
        exit;
    }
}

// Instantiate and handle the request
$handler = new McpProxyHandler();
$handler->handleRequest();
