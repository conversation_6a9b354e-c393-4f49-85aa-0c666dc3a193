<?php

function tavily_search($query, $max_results, $api_key) {
    if (empty($api_key) || $api_key === 'your_tavily_api_key_here' || $api_key === 'tvly-...') {
        return ['error' => 'Tavily API key 未在 config.php 中配置。'];
    }

    $payload = json_encode([
        'api_key' => $api_key,
        'query' => $query,
        'search_depth' => 'advanced', // 使用 "advanced" 以获得更高质量的结果
        'include_answer' => true,      // **FIX**: 改为 true 来获取总结性答案
        'include_raw_content' => false,
        'max_results' => (int) $max_results
    ]);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.tavily.com/search');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($payload)
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);

    $response = curl_exec($ch);
    $error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($error) {
        return ['error' => 'cURL Error: ' . $error];
    }
    
    if ($http_code !== 200) {
        return ['error' => 'Tavily API returned HTTP status ' . $http_code . '. Response: ' . $response];
    }

    return json_decode($response, true);
}
