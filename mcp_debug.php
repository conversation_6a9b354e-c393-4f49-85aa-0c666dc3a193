<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

$debug = [];

// 检查配置文件
$configPath = __DIR__ . '/mcp_config.json';
$debug['config_file_exists'] = file_exists($configPath);

if ($debug['config_file_exists']) {
    $debug['config_file_readable'] = is_readable($configPath);
    if ($debug['config_file_readable']) {
        $configContent = file_get_contents($configPath);
        $debug['config_content_length'] = strlen($configContent);
        $debug['config_content'] = $configContent;
        
        $config = json_decode($configContent, true);
        $debug['config_json_valid'] = json_last_error() === JSON_ERROR_NONE;
        if ($debug['config_json_valid']) {
            $debug['config_data'] = $config;
            $debug['server_count'] = count($config['mcpServers'] ?? []);
        } else {
            $debug['json_error'] = json_last_error_msg();
        }
    }
}

// 测试MCP服务器连接
if (isset($config['mcpServers'])) {
    $debug['server_tests'] = [];
    foreach ($config['mcpServers'] as $key => $server) {
        $url = $server['url'];
        $debug['server_tests'][$key] = [
            'url' => $url,
            'test_result' => 'testing...'
        ];
        
        // 简单的HTTP测试
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        $debug['server_tests'][$key]['http_code'] = $httpCode;
        $debug['server_tests'][$key]['curl_error'] = $error;
        $debug['server_tests'][$key]['test_result'] = $error ? 'failed' : 'success';
    }
}

// 检查PHP环境
$debug['php_version'] = PHP_VERSION;
$debug['curl_available'] = function_exists('curl_init');
$debug['json_available'] = function_exists('json_encode');

// 检查文件权限
$debug['directory_writable'] = is_writable(__DIR__);

echo json_encode($debug, JSON_PRETTY_PRINT);
?>
