<?php
// 测试MCP POST请求
header('Content-Type: application/json');

$server = 'fetch';
$sessionId = '99b26747-4e74-4d58-8d5f-f39738c27eda';
$testUrl = 'https://www.example.com';

$postData = json_encode(['url' => $testUrl]);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://a.6ird.com/mcp_proxy.php?action=post&server={$server}&sessionId={$sessionId}");
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo json_encode([
    'http_code' => $httpCode,
    'curl_error' => $error,
    'result' => $result,
    'post_data' => $postData
], JSON_PRETTY_PRINT);
?>
