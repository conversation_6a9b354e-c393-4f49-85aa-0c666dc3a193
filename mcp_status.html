<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP 状态检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #1a1a1a; color: #e0e0e0; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #2d5a2d; }
        .error { background-color: #5a2d2d; }
        .info { background-color: #2d4a5a; }
        button { padding: 10px 20px; margin: 5px; background-color: #4a4a4a; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #5a5a5a; }
        pre { background-color: #2a2a2a; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>MCP 状态检查</h1>
    
    <button onclick="checkStatus()">检查 MCP 状态</button>
    <button onclick="testSse()">测试 SSE 连接</button>
    
    <div id="results"></div>

    <script>
        async function checkStatus() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="info">正在检查 MCP 状态...</div>';
            
            try {
                // 测试服务器列表
                const listResponse = await fetch('./mcp_proxy.php?action=list');
                const listData = await listResponse.json();
                
                let html = '<div class="success">✅ MCP 代理工作正常</div>';
                html += '<div class="info">可用服务器:</div>';
                html += '<pre>' + JSON.stringify(listData, null, 2) + '</pre>';
                
                // 测试每个服务器的 SSE 连接
                for (const [serverName, serverConfig] of Object.entries(listData)) {
                    html += `<div class="info">测试 ${serverName} 服务器...</div>`;
                    
                    try {
                        const sseResponse = await fetch(`./mcp_proxy.php?action=sse&server=${serverName}`);
                        const sseText = await sseResponse.text();
                        
                        if (sseText.includes('sessionId')) {
                            html += `<div class="success">✅ ${serverName} SSE 连接正常</div>`;
                            html += `<pre>${sseText}</pre>`;
                        } else {
                            html += `<div class="error">❌ ${serverName} SSE 响应异常</div>`;
                            html += `<pre>${sseText}</pre>`;
                        }
                    } catch (error) {
                        html += `<div class="error">❌ ${serverName} SSE 连接失败: ${error.message}</div>`;
                    }
                }
                
                results.innerHTML = html;
                
            } catch (error) {
                results.innerHTML = `<div class="error">❌ MCP 代理连接失败: ${error.message}</div>`;
            }
        }
        
        function testSse() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="info">正在测试实时 SSE 连接...</div>';
            
            const eventSource = new EventSource('./mcp_proxy.php?action=sse&server=fetch');
            
            eventSource.onopen = () => {
                results.innerHTML += '<div class="success">✅ SSE 连接已建立</div>';
            };
            
            eventSource.onmessage = (event) => {
                results.innerHTML += `<div class="info">收到消息: ${event.data}</div>`;
                
                // 自动关闭连接
                setTimeout(() => {
                    eventSource.close();
                    results.innerHTML += '<div class="info">SSE 连接已关闭</div>';
                }, 2000);
            };
            
            eventSource.onerror = (error) => {
                results.innerHTML += `<div class="error">❌ SSE 连接错误</div>`;
                eventSource.close();
            };
        }
        
        // 页面加载时自动检查状态
        window.onload = checkStatus;
    </script>
</body>
</html>
