<?php
// Chat Hub Configuration File
// --------------------------
// 请在此文件中设置您的所有机密信息和模型。

// 1. 设置您的访问密码
// 出于安全考虑，建议使用复杂的密码。
define('CONFIG_PASSWORD', '520123');

// 2. 设置会话密钥
// 这是一个用于增强安全性的随机字符串。请务必修改为独一无二的内容。
// 您可以使用在线工具生成一个长而随机的字符串。
// 警告：请不要使用默认值！
define('SESSION_SECRET_KEY', '6ird1217');

// 3. Tavily 搜索 API 密钥 (可选)
// 如果需要网络搜索功能，请在此处填入您的 Tavily API 密钥。
// 可在 https://tavily.com 免费获取。
define('TAVILY_API_KEY', '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'); // <-- 替换为您的 Tavily API 密钥

// 4. 默认 AI 模型列表
// 用户可以在前端的设置中覆盖和添加自己的模型。
// 此处仅作为新用户或清除本地设置后的默认配置。
// 'id'       => 模型的唯一标识符。
// 'name'     => 显示在界面上的名称。
// 'api_url'  => 模型服务商提供的 API 端点。
// 'api_key'  => 您的 API 密钥 (建议留空，让用户在前端设置)。
// 'enabled'  => 是否默认在聊天网格中启用。
// 'search'   => 此模型是否支持网络搜索。
// 'icon'     => 图标的 URL 或 Lucide 图标名称 (https://lucide.dev/)。

function get_default_models() {
    return [
    [
        'id'      => 'gpt-4.1',
        'name'    => 'GPT-4.1',
        'api_url' => 'https://api.linn.pro/v1/chat/completions',
        'api_key' => 'sk-7cho3m8yuCz8HKTk7bFd424a7eA745B0B41369424f57E4D2', // <-- IMPORTANT: REPLACE THIS
        'enabled_by_default' => true,
        'supports_search' => true,
        'supports_vision' => true,
        'icon'    => './assets/openai.png',
    ],
    [
        'id'      => 'DeepSeek-R1',
        'name'    => 'DeepSeek-R1',
        'api_url' => 'https://api.linn.pro/v1/chat/completions',
        'api_key' => 'sk-7cho3m8yuCz8HKTk7bFd424a7eA745B0B41369424f57E4D2', // <-- IMPORTANT: REPLACE THIS
        'enabled_by_default' => true,
        'supports_search' => true,
        'supports_vision' => false,
        'icon'    => './assets/deepseek.png',
    ],
    [
        'id'      => 'Grok-3',
        'name'    => 'Grok-3',
        'api_url' => 'https://api.linn.pro/v1/chat/completions',
        'api_key' => 'sk-7cho3m8yuCz8HKTk7bFd424a7eA745B0B41369424f57E4D2', // <-- IMPORTANT: REPLACE THIS
        'enabled_by_default' =>false,
        'supports_search' => true,
        'supports_vision' => false,
        'icon'    => './assets/grok.png',
    ],
    [
        'id'      => 'gemini-2.5-flash-preview-05-20',
        'name'    => 'Gemini-2.5-flash',
        'api_url' => 'https://api.linn.pro/v1/chat/completions',
        'api_key' => 'sk-7cho3m8yuCz8HKTk7bFd424a7eA745B0B41369424f57E4D2', // <-- IMPORTANT: REPLACE THIS
        'enabled_by_default' => false,
        'supports_search' => true,
        'supports_vision' => true,
        'icon'    => './assets/gemini.png',
    ],    
    [
        'id'      => 'Qwen3-235B-A22B', // Even non-OpenAI models can be compatible
        'name'    => 'Qwen3-235B-A22B',
        'api_url' => 'https://api.linn.pro/v1/chat/completions', // <-- e.g., OpenRouter, Together.ai
        'api_key' => 'sk-7cho3m8yuCz8HKTk7bFd424a7eA745B0B41369424f57E4D2',
        'enabled_by_default' => false,
        'supports_search' => true,
        'supports_vision' => false,
        'icon'    => './assets/qwen.png',
    ],
    [
        'id'      => 'claude-3-7-sonnet-latest',
        'name'    => 'Claude-3-7-sonnet',
        'api_url' => 'https://api.linn.pro/v1/chat/completions',
        'api_key' => 'sk-7cho3m8yuCz8HKTk7bFd424a7eA745B0B41369424f57E4D2',
        'enabled_by_default' => false,
        'supports_search' => true,
        'supports_vision' => true,
        'icon'    => './assets/anthropic.png',
    ]
    ];
}

?>
