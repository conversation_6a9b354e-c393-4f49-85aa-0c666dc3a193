<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatHub - 6ird@rticles</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        /* Google Font */
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
        
        /* 基本样式 */
        html, body {
            height: 100%;
            overflow-y: hidden; /* 防止body滚动，让内部元素控制滚动 */
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar { width: 6px; }
        .custom-scrollbar::-webkit-scrollbar-track { background: transparent; }
        .custom-scrollbar::-webkit-scrollbar-thumb { background: #4a5568; border-radius: 3px; }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover { background: #718096; }

        /* Prose (Markdown 渲染) 样式优化 */
        .prose { color: #e2e8f0; }
        .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 { color: #fff; }
        .prose a { color: #63b3ed; text-decoration: none; }
        .prose a:hover { text-decoration: underline; }
        .prose strong { color: #fff; }
        .prose pre {
            background-color: #0d1117;
            border: 1px solid #30363d;
            padding: 1rem;
            border-radius: 0.5rem;
            position: relative;
            font-size: 0.9em;
        }
        .prose code { color: #a5d6ff; }
        .prose pre code { color: inherit; }
        .prose table th { background-color: #2d3748; }

        /* 动画效果 */
        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        @keyframes slideInUp { from { transform: translateY(20px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
        .fade-in { animation: fadeIn 0.5s ease-out forwards; }
        .slide-in-up { animation: slideInUp 0.5s ease-out forwards; }

        /* Modal 动画 */
        .modal-enter { opacity: 0; }
        .modal-enter-active { opacity: 1; transition: opacity 200ms ease-out; }
        .modal-panel-enter { transform: scale(0.95); opacity: 0; }
        .modal-panel-enter-active { transform: scale(1); opacity: 1; transition: all 200ms ease-out; }
        .modal-leave-active { opacity: 0; transition: opacity 200ms ease-in; }
        .modal-panel-leave-active { transform: scale(0.95); opacity: 0; transition: all 200ms ease-in; }

        /* 打字机效果光标 */
        .typing-cursor::after {
            content: '▍';
            animation: blink 1s step-end infinite;
            margin-left: 2px;
            font-size: 1.1em;
            color: #63b3ed;
        }
        @keyframes blink { 50% { opacity: 0; } }

        /* 修正下拉菜单颜色 */
        select {
            color-scheme: dark;
        }
        select option {
            background-color: #111827; /* Tailwind gray-900 (近黑色) */
            color: white;
        }
    </style>
</head>
<body class="bg-gray-900 text-white font-sans">

    <!-- 动态背景 -->
    <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-gray-900 via-gray-800 to-black -z-10"></div>
    
    <!-- 密码屏幕 -->
    <div id="password-screen" class="flex flex-col items-center justify-center h-full">
        <div class="w-full max-w-sm p-8 bg-gray-800/50 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-700/50">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold">ChatHub</h1>
                <p class="text-gray-400 mt-2">请输入访问密码</p>
            </div>
            <form id="password-form" class="space-y-6">
                <div>
                    <div class="relative">
                        <i data-lucide="key-round" class="absolute left-3.5 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-500"></i>
                        <input id="password-input" type="password" placeholder="密码" class="w-full bg-gray-700 border border-gray-600 rounded-lg p-3.5 pl-11 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-300">
                    </div>
                    <p id="password-error" class="text-red-400 text-sm mt-2 text-center hidden"></p>
                </div>
                <button type="submit" class="w-full flex items-center justify-center gap-2 p-3 bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors font-semibold shadow-lg hover:shadow-blue-500/50">
                    <i data-lucide="shield-check" class="w-5 h-5"></i>
                    授权访问
                </button>
            </form>
        </div>
    </div>

    <!-- 主应用 -->
    <main id="app" class="hidden h-full flex">
        <!-- 侧边栏 -->
        <aside id="sidebar" class="absolute md:relative z-30 md:z-auto w-72 bg-gray-800/70 backdrop-blur-md text-white h-full flex flex-col flex-shrink-0 transition-transform duration-300 ease-in-out -translate-x-full md:translate-x-0 border-r border-gray-700/50">
            <div class="flex items-center justify-between p-4 border-b border-gray-700/50">
                <h1 class="text-xl font-bold">ChatHub</h1>
                <button id="close-sidebar-btn" class="p-1 rounded-full hover:bg-gray-700 md:hidden">
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                </button>
            </div>
            <nav id="model-list" class="flex-1 p-4 space-y-2 overflow-y-auto custom-scrollbar">
                <!-- 模型列表将由 JS 插入 -->
            </nav>
            <div class="p-4 border-t border-gray-700/50 space-y-2">
                <div class="flex items-center justify-around">
                    <button id="settings-btn" class="p-2 rounded-lg hover:bg-gray-600 transition-colors" title="模型设置">
                        <i data-lucide="settings-2" class="w-5 h-5 text-gray-300"></i>
                    </button>
                    <button id="data-management-btn" class="p-2 rounded-lg hover:bg-gray-600 transition-colors" title="数据管理">
                        <i data-lucide="database" class="w-5 h-5 text-gray-300"></i>
                    </button>
                    <button id="announcement-btn" class="p-2 rounded-lg hover:bg-gray-600 transition-colors" title="公告">
                        <i data-lucide="siren" class="w-5 h-5 text-gray-300"></i>
                    </button>
                </div>
                <p id="auth-status" class="text-xs text-center text-gray-500 flex items-center justify-center gap-1.5 pt-2"><i data-lucide="shield-check" class="w-3 h-3 text-green-500"></i>已授权访问</p>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col relative bg-gray-900/80 overflow-hidden">
             <header class="flex items-center justify-between p-3 border-b border-gray-700/50 flex-shrink-0">
                <button id="open-sidebar-btn" class="md:hidden p-2 -ml-2 text-gray-400 hover:text-white">
                    <i data-lucide="menu" class="w-5 h-5"></i>
                </button>
                <div class="flex items-center gap-4">
                    <div id="layout-controls" class="flex items-center gap-1 p-1 bg-gray-800 border border-gray-700 rounded-lg">
                       <!-- 布局按钮由 JS 动态添加 -->
                    </div>
                    <button id="global-new-chat-btn" class="p-1.5 rounded-lg bg-gray-800 border border-gray-700 hover:bg-gray-700 transition-colors" title="全局新对话">
                        <i data-lucide="square-plus" class="w-5 h-5 text-gray-400 hover:text-white"></i>
                    </button>
                </div>
                <div class="w-8 md:w-0"></div> <!-- 占位符 -->
            </header>
            <div id="chat-grid" class="flex-1 p-4 overflow-y-auto gap-4 custom-scrollbar">
                 <!-- 聊天窗口将由 JS 插入 -->
            </div>
            <footer class="p-4 flex-shrink-0 bg-gray-900/50 border-t border-gray-700/50">
                <div class="max-w-4xl mx-auto mb-3 flex items-center gap-3">
                    <button id="parameter-settings-btn" class="p-2 rounded-lg hover:bg-gray-700" title="参数设置">
                        <i data-lucide="sliders-horizontal" class="w-5 h-5 text-gray-400"></i>
                    </button>
                    <button id="upload-file-btn" class="p-2 rounded-lg hover:bg-gray-700" title="上传文件或图片">
                        <i data-lucide="paperclip" class="w-5 h-5 text-gray-400"></i>
                    </button>
                    <input type="file" id="file-input" class="hidden" accept="image/*,.txt,.js,.py,.html,.css,.json,.csv,.md,.xml">
                    <button id="quick-phrases-btn" class="p-2 rounded-lg hover:bg-gray-700" title="快捷短语">
                        <i data-lucide="message-square-quote" class="w-5 h-5 text-gray-400"></i>
                    </button>
                    <button id="search-toggle-btn" class="p-2 rounded-lg hover:bg-gray-700 data-[active=true]:bg-blue-600/30 data-[active=true]:text-blue-300" title="网络搜索 (关闭)">
                        <i data-lucide="globe" class="w-5 h-5 text-gray-400"></i>
                    </button>
                    <div id="search-options" class="hidden items-center gap-2 p-1 bg-gray-700 border border-gray-600 rounded-lg">
                        <label for="search-results-count" class="text-xs text-gray-400 pl-1">结果:</label>
                        <select id="search-results-count" class="bg-transparent text-white border-none text-xs focus:outline-none">
                            <option value="3">3</option>
                            <option value="5" selected>5</option>
                            <option value="7">7</option>
                        </select>
                    </div>
                    <button id="mcp-toggle-btn" class="p-2 rounded-lg hover:bg-gray-700 data-[active=true]:bg-blue-600/30 data-[active=true]:text-blue-300" title="MCP模型上下文协议">
                        <i data-lucide="server-cog" class="w-5 h-5 text-gray-400"></i>
                    </button>
                    <div id="mcp-options" class="hidden items-center gap-2 flex-wrap p-1 bg-gray-700 border border-gray-600 rounded-lg">
                        <span class="text-xs text-gray-400">MCP:</span>
                        <div id="mcp-selection-area" class="flex items-center gap-2 flex-wrap">
                            <!-- MCP checkboxes will be rendered here by JS -->
                        </div>
                    </div>
                </div>
                <div id="file-attachment-indicator" class="max-w-4xl mx-auto mb-2"></div>
                <div class="relative max-w-4xl mx-auto">
                    <textarea id="chat-input" placeholder="输入问题 (Shift+Enter 换行)..." rows="1" class="w-full bg-gray-800 border border-gray-700 rounded-xl p-4 pl-4 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none custom-scrollbar transition-all duration-200"></textarea>
                    <button id="send-button" class="absolute right-3.5 top-1/2 -translate-y-1/2 p-2 bg-blue-600 rounded-lg hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed transition-transform duration-200 hover:scale-110">
                        <i data-lucide="send" class="w-5 h-5"></i>
                    </button>
                </div>
            </footer>
        </div>
    </main>

    <!-- Modal 容器 -->
    <div id="modal-container"></div>
    
    <script src="script.js" defer></script>
</body>
</html>
