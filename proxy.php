<?php
// Chat <PERSON>b Backend Proxy
// ----------------------
// v2.15 - Corrected cURL write function to proxy the raw SSE stream without modification, fixing JSON parsing errors on the client.

ini_set('display_errors', 0);
error_reporting(0);

// 引入配置文件和搜索功能
require_once 'config.php';
require_once 'tavily_search.php';

// 安全检查：确保用户已修改默认的会话密钥
if (defined('SESSION_SECRET_KEY') && SESSION_SECRET_KEY === 'your-super-secret-key-here-change-me') {
    header("HTTP/1.1 500 Internal Server Error");
    echo json_encode([
        'success' => false, 
        'message' => '严重安全警告：请在 config.php 文件中修改默认的 SESSION_SECRET_KEY！'
    ]);
    exit;
}

// #################### ROUTER LOGIC ####################

$action = $_POST['action'] ?? $_GET['action'] ?? '';

switch ($action) {
    case 'login':
        handle_login();
        break;
    case 'verify':
        handle_verify();
        break;
    case 'chat':
        handle_chat();
        break;
    case 'get_defaults':
        handle_get_defaults();
        break;
    case 'get_announcement':
        handle_get_announcement();
        break;
    default:
        header("HTTP/1.1 400 Bad Request");
        echo json_encode(['success' => false, 'message' => '无效的操作']);
        exit;
}

// #################### HANDLER FUNCTIONS ####################

function handle_get_announcement() {
    $announcement_file = 'announcement.md';
    if (file_exists($announcement_file)) {
        header('Content-Type: text/markdown; charset=utf-8');
        readfile($announcement_file);
    } else {
        header("HTTP/1.1 404 Not Found");
        echo "公告文件未找到。";
    }
    exit;
}


function handle_login() {
    header('Content-Type: application/json');
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo json_encode(['success' => false, 'message' => '无效的请求方法']);
        exit;
    }

    $password = $_POST['password'] ?? '';

    if (password_verify($password, password_hash(CONFIG_PASSWORD, PASSWORD_DEFAULT))) {
        $token = hash('sha256', SESSION_SECRET_KEY . CONFIG_PASSWORD);
        echo json_encode([
            'success' => true, 
            'token'   => $token,
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => '密码错误']);
    }
    exit;
}

function handle_verify() {
    header('Content-Type: application/json');
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo json_encode(['success' => false, 'message' => '无效的请求方法']);
        exit;
    }
    
    $token = $_POST['token'] ?? '';
    $expected_token = hash('sha256', SESSION_SECRET_KEY . CONFIG_PASSWORD);

    if ($token === $expected_token) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => '无效的会话']);
    }
    exit;
}

function handle_get_defaults() {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'models'  => get_default_models()
    ]);
    exit;
}

function handle_chat() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        header("HTTP/1.1 405 Method Not Allowed");
        exit('错误：此端点仅接受 POST 请求。');
    }

    ignore_user_abort(true);
    $raw_post_data = file_get_contents('php://input');
    $post_data = json_decode($raw_post_data, true);

    if (json_last_error() !== JSON_ERROR_NONE || !isset($post_data['modelConfig'], $post_data['history'])) {
        header("HTTP/1.1 400 Bad Request");
        exit('错误：请求体中的 JSON 无效或缺少参数。');
    }

    $model_config = $post_data['modelConfig'];
    $history = $post_data['history'];
    $use_search = $post_data['use_search'] ?? false;
    $max_results = $post_data['max_results'] ?? 5;
    $temperature = $post_data['temperature'] ?? 0.7;
    $search_results = null;

    $is_search_supported = $model_config['search'] ?? $model_config['supports_search'] ?? false;
    if ($use_search && !$is_search_supported) {
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        $unsupported_message = "当前AI模型不支持网络搜索功能。";
        echo "data: " . json_encode(['content' => $unsupported_message]) . "\n\n";
        echo "data: [DONE]\n\n";
        flush();
        exit;
    }

    if ($use_search && !empty($history) && $is_search_supported) {
        $last_message_content = '';
        $last_message = end($history);
        if ($last_message && $last_message['role'] === 'user') {
            if (is_array($last_message['content'])) {
                foreach($last_message['content'] as $part) {
                    if ($part['type'] === 'text') {
                        $last_message_content = $part['text'];
                        break;
                    }
                }
            } else {
                $last_message_content = $last_message['content'];
            }
        }
        
        if (!empty($last_message_content)) {
            $search_results = tavily_search($last_message_content, $max_results, TAVILY_API_KEY);

            if (isset($search_results['error'])) {
                 header('Content-Type: text/event-stream');
                 $error_message = "网络搜索失败: " . $search_results['error'];
                 echo "data: " . json_encode(['content' => $error_message]) . "\n\n";
                 echo "data: [DONE]\n\n";
                 flush();
                 exit;
            }
            
            $search_context = "你是一个AI研究助手。请使用以下提供的搜索结果来回答用户的问题。在你的回答中，必须使用 `[数字]` 的格式来引用你所使用的信息来源。例如：根据来源[1]，...。";

            if (!empty($search_results['answer'])) {
                $search_context .= "\n\n首先，这是一个基于搜索结果的快速总结答案，你可以参考它，但仍需结合并引用下方的具体来源进行回答：\n" . $search_results['answer'];
            }

            if (!empty($search_results['results'])) {
                $search_context .= "\n\n详细搜索结果如下：\n\n";
                $count = 1;
                foreach($search_results['results'] as $result) {
                    $search_context .= "[" . $count++ . "] 标题: " . $result['title'] . "\n链接: " . $result['url'] . "\n内容: " . $result['content'] . "\n\n";
                }
            }

            if (!empty($search_context)) {
                $system_message = ["role" => "system", "content" => trim($search_context)];
                array_splice($history, -1, 0, [$system_message]);
            }
        }
    }

    header('Content-Type: text/event-stream');
    header('Cache-Control: no-cache');
    header('Connection: keep-alive');
    
    $payload = [
        'model' => $model_config['id'],
        'messages' => $history,
        'stream' => true,
        'temperature' => (float)$temperature,
    ];

    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $model_config['api_key'],
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $model_config['api_url']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 300);

    // **FIX**: Correctly proxy the raw stream without modification.
    curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $chunk) {
        if (connection_aborted()) {
            return -1;
        }
        echo $chunk;
        @ob_flush();
        @flush();
        return strlen($chunk);
    });

    curl_exec($ch);
    
    if (!empty($search_results['results'])) {
        echo "event: sources\n";
        echo "data: " . json_encode($search_results['results']) . "\n\n";
        flush();
    }

    if (curl_errno($ch) > 0 && curl_errno($ch) !== 23) {
        $error_msg = curl_error($ch);
        $error_payload = json_encode(['content' => 'cURL 错误: ' . $error_msg]);
        echo "data: " . $error_payload . "\n\n";
    }
    
    echo "data: [DONE]\n\n";
    flush();
    
    curl_close($ch);
    exit;
}
?>
